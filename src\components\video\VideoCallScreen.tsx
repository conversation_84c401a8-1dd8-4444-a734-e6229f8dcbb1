import React, { useState, useEffect } from 'react';
import Button from '../ui/Button';

interface VideoCallScreenProps {
  onNavigate: (screen: string) => void;
}

const VideoCallScreen: React.FC<VideoCallScreenProps> = ({ onNavigate }) => {
  const [isCallActive, setIsCallActive] = useState(true);
  const [isMuted, setIsMuted] = useState(false);
  const [isVideoOff, setIsVideoOff] = useState(false);
  const [showHearts, setShowHearts] = useState(false);
  const [callDuration, setCallDuration] = useState(0);

  useEffect(() => {
    const interval = window.setInterval(() => {
      setCallDuration(prev => prev + 1);
    }, 1000);

    return () => window.clearInterval(interval);
  }, []);

  const formatDuration = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  const handleHeartReaction = () => {
    setShowHearts(true);
    window.setTimeout(() => setShowHearts(false), 2000);
  };

  const endCall = () => {
    setIsCallActive(false);
    window.setTimeout(() => onNavigate('chat'), 1000);
  };

  return (
    <div className="min-h-screen bg-black relative overflow-hidden">
      {/* Vine borders */}
      <div className="absolute inset-0 pointer-events-none">
        <div className="absolute top-0 left-0 w-full h-8 bg-gradient-to-r from-eden-600 via-eden-500 to-eden-600 opacity-80">
          <div className="flex justify-center items-center h-full text-white text-sm font-comic">
            🌿 Garden Video Call 🌿
          </div>
        </div>
        <div className="absolute bottom-0 left-0 w-full h-8 bg-gradient-to-r from-eden-600 via-eden-500 to-eden-600 opacity-80"></div>
        <div className="absolute top-8 left-0 w-4 h-full bg-gradient-to-b from-eden-600 via-eden-500 to-eden-600 opacity-60"></div>
        <div className="absolute top-8 right-0 w-4 h-full bg-gradient-to-b from-eden-600 via-eden-500 to-eden-600 opacity-60"></div>
      </div>

      {/* Main video area */}
      <div className="relative h-screen flex flex-col">
        {/* Remote video (full screen) */}
        <div className="flex-1 relative bg-gradient-to-br from-gray-800 to-gray-900 m-8 mt-16 mb-32 rounded-2xl overflow-hidden">
          <img
            src="https://images.unsplash.com/photo-1494790108755-2616b612b786?w=600"
            alt="Eve"
            className="w-full h-full object-cover"
            crossOrigin="anonymous"
          />
          
          {/* Call info overlay */}
          <div className="absolute top-4 left-4 right-4 flex justify-between items-center">
            <div className="bg-black/50 backdrop-blur-sm rounded-full px-4 py-2">
              <span className="text-white font-comic text-sm">Eve</span>
            </div>
            <div className="bg-black/50 backdrop-blur-sm rounded-full px-4 py-2">
              <span className="text-white font-comic text-sm">{formatDuration(callDuration)}</span>
            </div>
          </div>

          {/* Floating apples animation */}
          {showHearts && (
            <div className="absolute inset-0 pointer-events-none">
              {[...Array(6)].map((_, i) => (
                <div
                  key={i}
                  className="absolute text-3xl animate-float"
                  style={{
                    left: `${20 + i * 15}%`,
                    top: `${30 + (i % 2) * 20}%`,
                    animationDelay: `${i * 0.2}s`,
                    animationDuration: '2s'
                  }}
                >
                  ❤️
                </div>
              ))}
            </div>
          )}

          {/* Snake animation (when call goes well) */}
          <div className="absolute bottom-4 left-4 text-2xl animate-wiggle opacity-70">
            🐍
          </div>
        </div>

        {/* Local video (small overlay) */}
        <div className="absolute top-24 right-12 w-24 h-32 bg-gradient-to-br from-gray-700 to-gray-800 rounded-xl overflow-hidden border-2 border-white shadow-lg">
          {!isVideoOff ? (
            <img
              src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=200"
              alt="You"
              className="w-full h-full object-cover"
              crossOrigin="anonymous"
            />
          ) : (
            <div className="w-full h-full flex items-center justify-center text-white">
              <i className="bi bi-camera-video-off text-2xl"></i>
            </div>
          )}
        </div>

        {/* Call controls */}
        <div className="absolute bottom-12 left-1/2 transform -translate-x-1/2">
          <div className="flex items-center gap-4 bg-black/70 backdrop-blur-sm rounded-full px-6 py-4">
            <button
              onClick={() => setIsMuted(!isMuted)}
              className={`w-12 h-12 rounded-full flex items-center justify-center transition-colors ${
                isMuted ? 'bg-forbidden-500 text-white' : 'bg-white/20 text-white hover:bg-white/30'
              }`}
            >
              <i className={`bi ${isMuted ? 'bi-mic-mute' : 'bi-mic'} text-lg`}></i>
            </button>

            <button
              onClick={() => setIsVideoOff(!isVideoOff)}
              className={`w-12 h-12 rounded-full flex items-center justify-center transition-colors ${
                isVideoOff ? 'bg-forbidden-500 text-white' : 'bg-white/20 text-white hover:bg-white/30'
              }`}
            >
              <i className={`bi ${isVideoOff ? 'bi-camera-video-off' : 'bi-camera-video'} text-lg`}></i>
            </button>

            <button
              onClick={handleHeartReaction}
              className="w-12 h-12 rounded-full bg-paradise-500 text-white flex items-center justify-center hover:bg-paradise-600 transition-colors"
            >
              <i className="bi bi-heart text-lg"></i>
            </button>

            <button
              onClick={endCall}
              className="w-12 h-12 rounded-full bg-forbidden-500 text-white flex items-center justify-center hover:bg-forbidden-600 transition-colors"
            >
              <i className="bi bi-telephone-x text-lg"></i>
            </button>

            <button className="w-12 h-12 rounded-full bg-white/20 text-white flex items-center justify-center hover:bg-white/30 transition-colors">
              <i className="bi bi-three-dots text-lg"></i>
            </button>
          </div>
        </div>

        {/* Sound filters indicator */}
        <div className="absolute top-32 left-12 bg-black/50 backdrop-blur-sm rounded-full px-3 py-1">
          <span className="text-white text-xs font-comic">🌳 Garden Echo</span>
        </div>
      </div>

      {/* Call ended overlay */}
      {!isCallActive && (
        <div className="absolute inset-0 bg-black/80 flex items-center justify-center">
          <div className="text-center text-white">
            <div className="text-6xl mb-4">📞</div>
            <h2 className="text-2xl font-comic font-bold mb-2">Call Ended</h2>
            <p className="text-gray-300 font-inter">
              Duration: {formatDuration(callDuration)}
            </p>
            <p className="text-sm text-gray-400 mt-2 font-comic">
              "That was more tempting than the forbidden fruit! 🍎"
            </p>
          </div>
        </div>
      )}
    </div>
  );
};

export default VideoCallScreen;
