import React from 'react';

interface CardProps {
  children: React.ReactNode;
  className?: string;
  onClick?: () => void;
  hover?: boolean;
}

const Card: React.FC<CardProps> = ({
  children,
  className = '',
  onClick,
  hover = false
}) => {
  return (
    <div
      onClick={onClick}
      className={`bg-white rounded-2xl shadow-lg border border-eden-100 ${
        hover ? 'hover:shadow-xl hover:scale-105 transition-all duration-300 cursor-pointer' : ''
      } ${className}`}
    >
      {children}
    </div>
  );
};

export default Card;
