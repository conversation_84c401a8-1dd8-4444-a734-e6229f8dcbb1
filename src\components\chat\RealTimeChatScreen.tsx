import React, { useState, useEffect, useRef } from 'react';
import Button from '../ui/Button';
import Card from '../ui/Card';
import { useRealTimeChat } from '../../hooks/useRealTimeChat';
import { useRealTimeMatches } from '../../hooks/useRealTimeMatches';
import { useTypingIndicator } from '../../hooks/useTypingIndicator';
import { useOnlineStatus } from '../../hooks/useOnlineStatus';
import { useAuth } from '../../contexts/AuthContext';

interface RealTimeChatScreenProps {
  onNavigate: (screen: string) => void;
}

const RealTimeChatScreen: React.FC<RealTimeChatScreenProps> = ({ onNavigate }) => {
  const [selectedMatchId, setSelectedMatchId] = useState<string | null>(null);
  const [newMessage, setNewMessage] = useState('');
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const { user } = useAuth();
  const { matches, loading: matchesLoading } = useRealTimeMatches();
  const { 
    messages, 
    loading: messagesLoading, 
    sending, 
    sendMessage: sendNewMessage,
    markAsRead,
    unreadCount 
  } = useRealTimeChat({ matchId: selectedMatchId || '' });
  
  const { 
    otherUserTyping, 
    sendTypingIndicator 
  } = useTypingIndicator({ matchId: selectedMatchId || '' });
  
  const { isUserOnline } = useOnlineStatus();

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  // Mark messages as read when viewing a chat
  useEffect(() => {
    if (selectedMatchId && messages.length > 0) {
      const unreadMessageIds = messages
        .filter(msg => msg.sender_id !== user?.id && !msg.read_at)
        .map(msg => msg.id);
      
      if (unreadMessageIds.length > 0) {
        markAsRead(unreadMessageIds);
      }
    }
  }, [selectedMatchId, messages, user, markAsRead]);

  const sendMessage = async () => {
    if (newMessage.trim() && selectedMatchId) {
      const success = await sendNewMessage(newMessage.trim());
      if (success) {
        setNewMessage('');
        sendTypingIndicator(false);
      }
    }
  };

  const sendApple = async () => {
    if (selectedMatchId) {
      await sendNewMessage('🍎', 'emoji');
    }
  };

  const getOtherUser = (match: any) => {
    if (!user) return null;
    return match.user1_id === user.id ? match.user2 : match.user1;
  };

  const selectedMatch = matches.find(match => match.id === selectedMatchId);
  const otherUser = selectedMatch ? getOtherUser(selectedMatch) : null;

  if (selectedMatch) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-eden-50 via-paradise-50 to-forbidden-50 flex flex-col">
        {/* Chat Header */}
        <div className="bg-white shadow-sm border-b border-eden-100 p-4">
          <div className="flex items-center gap-3 max-w-md mx-auto">
            <button
              onClick={() => setSelectedMatchId(null)}
              className="p-2 rounded-full hover:bg-gray-100 transition-colors"
            >
              <i className="bi bi-arrow-left text-xl text-gray-600"></i>
            </button>
            
            <div className="relative">
              <img
                src={otherUser?.photos?.[0] || 'https://placehold.co/40x40/e5e7eb/6b7280?text=?'}
                alt={otherUser?.name || 'User'}
                className="w-10 h-10 rounded-full object-cover"
                crossOrigin="anonymous"
              />
              {otherUser && isUserOnline(otherUser.id) && (
                <div className="absolute -bottom-1 -right-1 w-3 h-3 bg-eden-500 rounded-full border-2 border-white"></div>
              )}
            </div>
            
            <div className="flex-1">
              <h2 className="font-comic font-semibold text-gray-800">
                {otherUser?.name || 'Unknown User'}
              </h2>
              <p className="text-xs text-gray-500">
                {otherUserTyping ? (
                  <span className="text-eden-600 animate-pulse">typing...</span>
                ) : otherUser && isUserOnline(otherUser.id) ? (
                  'Online now'
                ) : (
                  'Last seen recently'
                )}
              </p>
            </div>
            
            <button className="p-2 rounded-full hover:bg-gray-100 transition-colors">
              <i className="bi bi-telephone text-xl text-gray-600"></i>
            </button>
            
            <button
              onClick={() => onNavigate('video-call')}
              className="p-2 rounded-full hover:bg-gray-100 transition-colors"
            >
              <i className="bi bi-camera-video text-xl text-gray-600"></i>
            </button>
          </div>
        </div>

        {/* Messages */}
        <div className="flex-1 p-4 max-w-md mx-auto w-full overflow-y-auto">
          {messagesLoading ? (
            <div className="flex justify-center items-center h-full">
              <div className="text-center">
                <div className="text-4xl mb-2 animate-pulse">💬</div>
                <p className="text-gray-500">Loading messages...</p>
              </div>
            </div>
          ) : (
            <div className="space-y-4">
              {messages.map((message) => (
                <div
                  key={message.id}
                  className={`flex ${message.sender_id === user?.id ? 'justify-end' : 'justify-start'}`}
                >
                  <div
                    className={`max-w-xs px-4 py-2 rounded-2xl ${
                      message.sender_id === user?.id
                        ? 'bg-eden-500 text-white'
                        : 'bg-white text-gray-800 border border-gray-200'
                    }`}
                  >
                    {message.type === 'emoji' && message.content === '🍎' ? (
                      <div className="text-center">
                        <div className="text-3xl animate-bounce">🍎</div>
                        <p className="text-xs mt-1 opacity-75">Sent a forbidden apple</p>
                      </div>
                    ) : message.type === 'emoji' ? (
                      <div className="text-center text-2xl">{message.content}</div>
                    ) : (
                      <p className="font-inter text-sm">{message.content}</p>
                    )}
                    <div className="text-xs opacity-50 mt-1">
                      {new Date(message.created_at).toLocaleTimeString([], { 
                        hour: '2-digit', 
                        minute: '2-digit' 
                      })}
                      {message.read_at && message.sender_id === user?.id && (
                        <span className="ml-1">✓</span>
                      )}
                    </div>
                  </div>
                </div>
              ))}
              
              {/* Typing Indicator */}
              {otherUserTyping && (
                <div className="flex justify-start">
                  <div className="bg-white text-gray-800 border border-gray-200 px-4 py-2 rounded-2xl max-w-xs">
                    <div className="flex items-center space-x-1">
                      <div className="flex space-x-1">
                        <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                        <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                        <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                      </div>
                      <span className="text-xs text-gray-500 ml-2">typing...</span>
                    </div>
                  </div>
                </div>
              )}
              
              <div ref={messagesEndRef} />
            </div>
          )}
        </div>

        {/* Message Input */}
        <div className="bg-white border-t border-gray-200 p-4">
          <div className="flex items-center gap-3 max-w-md mx-auto">
            <button
              onClick={sendApple}
              className="p-3 rounded-full bg-forbidden-500 text-white hover:bg-forbidden-600 transition-colors"
            >
              <i className="bi bi-apple text-lg"></i>
            </button>
            
            <div className="flex-1 flex items-center bg-gray-100 rounded-full px-4 py-2">
              <input
                type="text"
                value={newMessage}
                onChange={(e) => {
                  setNewMessage(e.target.value);
                  sendTypingIndicator(e.target.value.length > 0);
                }}
                onBlur={() => sendTypingIndicator(false)}
                placeholder="Type a tempting message..."
                className="flex-1 bg-transparent outline-none font-inter text-sm"
                onKeyPress={(e) => e.key === 'Enter' && sendMessage()}
                disabled={sending}
              />
              <button className="p-1 text-gray-500 hover:text-gray-700">
                <i className="bi bi-emoji-smile"></i>
              </button>
            </div>
            
            <button
              onClick={sendMessage}
              disabled={!newMessage.trim() || sending}
              className="p-3 rounded-full bg-eden-500 text-white hover:bg-eden-600 transition-colors disabled:opacity-50"
            >
              {sending ? (
                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
              ) : (
                <i className="bi bi-send text-lg"></i>
              )}
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-eden-50 via-paradise-50 to-forbidden-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b border-eden-100 p-4">
        <div className="flex items-center justify-between max-w-md mx-auto">
          <button
            onClick={() => onNavigate('home')}
            className="p-2 rounded-full hover:bg-gray-100 transition-colors"
          >
            <i className="bi bi-arrow-left text-xl text-gray-600"></i>
          </button>
          
          <div className="text-center">
            <h1 className="text-xl font-comic font-bold text-gray-800">
              💬 Forbidden Chats
            </h1>
            <p className="text-xs text-gray-500">Your tempting conversations</p>
          </div>
          
          <button className="p-2 rounded-full hover:bg-gray-100 transition-colors">
            <i className="bi bi-search text-xl text-gray-600"></i>
          </button>
        </div>
      </div>

      {/* Matches List */}
      <div className="p-4 max-w-md mx-auto">
        {matchesLoading ? (
          <div className="text-center py-12">
            <div className="text-4xl mb-2 animate-pulse">💕</div>
            <p className="text-gray-500">Loading your matches...</p>
          </div>
        ) : (
          <div className="space-y-3">
            {matches.map((match) => {
              const otherUser = getOtherUser(match);
              if (!otherUser) return null;
              
              return (
                <Card
                  key={match.id}
                  hover
                  onClick={() => setSelectedMatchId(match.id)}
                  className="p-4"
                >
                  <div className="flex items-center gap-4">
                    <div className="relative">
                      <img
                        src={otherUser.photos?.[0] || 'https://placehold.co/60x60/e5e7eb/6b7280?text=?'}
                        alt={otherUser.name}
                        className="w-15 h-15 rounded-full object-cover"
                        crossOrigin="anonymous"
                      />
                      {isUserOnline(otherUser.id) && (
                        <div className="absolute -bottom-1 -right-1 w-4 h-4 bg-eden-500 rounded-full border-2 border-white"></div>
                      )}
                    </div>
                    
                    <div className="flex-1">
                      <div className="flex items-center justify-between mb-1">
                        <h3 className="font-comic font-semibold text-gray-800">
                          {otherUser.name}
                        </h3>
                        <span className="text-xs text-gray-400">
                          {new Date(match.updated_at).toLocaleTimeString([], { 
                            hour: '2-digit', 
                            minute: '2-digit' 
                          })}
                        </span>
                      </div>
                      
                      <p className="text-sm text-gray-600 font-inter">
                        {match.status === 'matched' 
                          ? 'You have a new match! 💕'
                          : 'Start a conversation...'
                        }
                      </p>
                    </div>
                    
                    <div className="text-right">
                      <i className="bi bi-chevron-right text-gray-400"></i>
                    </div>
                  </div>
                </Card>
              );
            })}
          </div>
        )}

        {!matchesLoading && matches.length === 0 && (
          <div className="text-center py-12">
            <div className="text-6xl mb-4">💔</div>
            <h3 className="text-xl font-comic font-semibold text-gray-600 mb-2">
              No matches yet
            </h3>
            <p className="text-gray-500 font-inter">
              Start swiping to find your forbidden match!
            </p>
            <Button
              onClick={() => onNavigate('home')}
              variant="forbidden"
              className="mt-4"
            >
              Start Matching
            </Button>
          </div>
        )}
      </div>
    </div>
  );
};

export default RealTimeChatScreen;
