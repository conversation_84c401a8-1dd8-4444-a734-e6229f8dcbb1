# Adem & Eve - Forbidden Match Dating App

🍎 A unique dating application inspired by the timeless tale of paradise, where love knows no boundaries and every connection is a tempting adventure.

## 🌟 Features

- **Smart Matching Algorithm**: Find your perfect complement based on preferences and personality
- **Secure Messaging**: End-to-end encrypted conversations
- **Location-Based Discovery**: Find matches near you or explore worldwide
- **Anonymous Confessions**: Share thoughts in the Confession Tree
- **Snake Bot Wisdom**: Get personalized dating advice from AI
- **Paradise-Themed UI**: Beautiful interface capturing the essence of paradise

## 🚀 Getting Started

### Prerequisites

- Node.js 18+ 
- Java 17+
- Android Studio (for Android development)
- Android SDK

### Installation

1. Clone the repository
```bash
git clone <repository-url>
cd websparks_adem_&_eve_–_forbidden_match_dating_app_ezeeii
```

2. Install dependencies
```bash
npm install
```

3. Build the web application
```bash
npm run build
```

4. Sync with Capacitor
```bash
npm run capacitor:sync
```

### Development

#### Web Development
```bash
npm run dev
```

#### Android Development
```bash
npm run android:dev
```

#### Build for Production
```bash
npm run android:build
```

## 📱 Mobile App Structure

```
src/
├── components/
│   ├── auth/           # Authentication screens
│   ├── onboarding/     # User onboarding flow
│   ├── home/           # Main matching interface
│   ├── chat/           # Messaging system
│   ├── profile/        # User profiles
│   ├── map/            # Location-based features
│   ├── video/          # Video calling
│   └── ui/             # Reusable UI components
├── types/              # TypeScript type definitions
└── App.tsx             # Main application component
```

## 🔧 Configuration

### Capacitor Configuration
The app uses Capacitor for native mobile functionality. Configuration is in `capacitor.config.ts`.

### Android Configuration
- Package ID: `com.ademeve.forbiddenmatch`
- Target SDK: 34
- Min SDK: 22

## 🏗️ Build Process

### Debug Build
```bash
cd android
./gradlew assembleDebug
```

### Release Build (for Google Play Store)
```bash
cd android
./gradlew bundleRelease
```

## 📦 Google Play Store Deployment

### Requirements Checklist
- ✅ App icons (all sizes)
- ✅ Splash screens
- ✅ Privacy policy
- ✅ Terms of service
- ✅ App metadata
- ✅ Screenshots
- ✅ Feature graphics
- ✅ Content rating
- ✅ Signed APK/AAB

### Store Listing
- **Category**: Dating
- **Content Rating**: Mature 17+
- **Target Audience**: Adults 18+

## 🔒 Privacy & Security

- End-to-end encryption for messages
- Secure user authentication
- Privacy-compliant data handling
- GDPR compliance ready

## 🛠️ Technologies Used

- **Frontend**: React + TypeScript
- **Styling**: Tailwind CSS
- **Mobile**: Capacitor
- **Build Tool**: Vite
- **Platform**: Android (iOS support planned)

## 📄 Documentation

- [Privacy Policy](privacy-policy.md)
- [Terms of Service](terms-of-service.md)
- [Google Play Metadata](google-play-metadata.md)
- [Release Signing Guide](android/app/release-signing-guide.md)

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📞 Support

- Email: <EMAIL>
- Website: https://your-domain.com

## 📜 License

This project is proprietary software. All rights reserved.

## 🚀 Deployment Status

- ✅ Web build configured
- ✅ Android platform added
- ✅ Capacitor configured
- ✅ Google Play Store ready
- 🔄 Pending: Final APK build and store submission

---

*Made with ❤️ for finding forbidden love in the digital paradise*
