{"name": "adem-eve-forbidden-match", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview", "android": "cap run android", "android:build": "npm run build && cap sync && cap build android", "android:dev": "cap run android --livereload --external", "capacitor:sync": "cap sync", "capacitor:build": "npm run build && cap sync"}, "dependencies": {"@capacitor/android": "^6.0.0", "@capacitor/app": "^6.0.0", "@capacitor/cli": "^6.0.0", "@capacitor/core": "^6.0.0", "@capacitor/splash-screen": "^6.0.0", "@capacitor/status-bar": "^6.0.0", "@supabase/supabase-js": "^2.49.9", "lucide-react": "^0.344.0", "react": "^18.3.1", "react-dom": "^18.3.1"}, "devDependencies": {"@eslint/js": "^9.9.1", "@types/react": "^18.3.5", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.18", "eslint": "^9.9.1", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.11", "globals": "^15.9.0", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "typescript": "^5.5.3", "typescript-eslint": "^8.3.0", "vite": "^5.4.2"}}