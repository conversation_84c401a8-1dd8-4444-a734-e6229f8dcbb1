-- Sample data for testing real-time chat

-- First, let's create some test matches
INSERT INTO matches (id, user1_id, user2_id, status, created_at, updated_at) VALUES
(
  '11111111-1111-1111-1111-111111111111',
  '550e8400-e29b-41d4-a716-446655440001', -- Eve
  '550e8400-e29b-41d4-a716-446655440002', -- Adam
  'matched',
  NOW() - INTERVAL '2 hours',
  NOW() - INTERVAL '1 hour'
),
(
  '22222222-2222-2222-2222-222222222222',
  '550e8400-e29b-41d4-a716-446655440001', -- Eve
  '550e8400-e29b-41d4-a716-446655440003', -- <PERSON><PERSON><PERSON>
  'matched',
  NOW() - INTERVAL '1 day',
  NOW() - INTERVAL '30 minutes'
);

-- Sample messages for the Eve-Adam match
INSERT INTO messages (id, match_id, sender_id, content, type, created_at) VALUES
(
  'msg-1111-1111-1111-111111111111',
  '11111111-1111-1111-1111-111111111111',
  '550e8400-e29b-41d4-a716-446655440002', -- Adam
  'Hey Eve! I couldn''t help but notice your love for forbidden fruits 🍎',
  'text',
  NOW() - INTERVAL '1 hour'
),
(
  'msg-2222-2222-2222-222222222222',
  '11111111-1111-1111-1111-111111111111',
  '550e8400-e29b-41d4-a716-446655440001', -- Eve
  'Adam! Finally someone who understands the temptation 😏',
  'text',
  NOW() - INTERVAL '55 minutes'
),
(
  'msg-3333-3333-3333-333333333333',
  '11111111-1111-1111-1111-111111111111',
  '550e8400-e29b-41d4-a716-446655440002', -- Adam
  '🍎',
  'emoji',
  NOW() - INTERVAL '50 minutes'
),
(
  'msg-4444-4444-4444-444444444444',
  '11111111-1111-1111-1111-111111111111',
  '550e8400-e29b-41d4-a716-446655440001', -- Eve
  'Aww, you sent me a forbidden apple! How sweet 💕',
  'text',
  NOW() - INTERVAL '45 minutes'
),
(
  'msg-5555-5555-5555-555555555555',
  '11111111-1111-1111-1111-111111111111',
  '550e8400-e29b-41d4-a716-446655440002', -- Adam
  'Want to share it in the garden sometime? 🌳',
  'text',
  NOW() - INTERVAL '40 minutes'
);

-- Sample messages for the Eve-Serpentina match
INSERT INTO messages (id, match_id, sender_id, content, type, created_at) VALUES
(
  'msg-6666-6666-6666-666666666666',
  '22222222-2222-2222-2222-222222222222',
  '550e8400-e29b-41d4-a716-446655440003', -- Serpentina
  'Well, well... look who found their way to the forbidden side 🐍',
  'text',
  NOW() - INTERVAL '30 minutes'
),
(
  'msg-7777-7777-7777-777777777777',
  '22222222-2222-2222-2222-222222222222',
  '550e8400-e29b-41d4-a716-446655440001', -- Eve
  'Serpentina! I should have known you''d be here tempting hearts 😈',
  'text',
  NOW() - INTERVAL '25 minutes'
),
(
  'msg-8888-8888-8888-888888888888',
  '22222222-2222-2222-2222-222222222222',
  '550e8400-e29b-41d4-a716-446655440003', -- Serpentina
  'Guilty as charged. Care to learn some of my secrets? 🔮',
  'text',
  NOW() - INTERVAL '20 minutes'
);

-- Mark some messages as read
UPDATE messages SET read_at = NOW() - INTERVAL '30 minutes' 
WHERE id IN ('msg-1111-1111-1111-111111111111', 'msg-2222-2222-2222-222222222222', 'msg-3333-3333-3333-333333333333');

-- Create some user interactions for potential matches
INSERT INTO user_interactions (user_id, target_user_id, interaction_type, created_at) VALUES
-- Eve likes Adam (already matched)
('550e8400-e29b-41d4-a716-446655440001', '550e8400-e29b-41d4-a716-446655440002', 'like', NOW() - INTERVAL '3 hours'),
-- Adam likes Eve (already matched)
('550e8400-e29b-41d4-a716-446655440002', '550e8400-e29b-41d4-a716-446655440001', 'like', NOW() - INTERVAL '2 hours 30 minutes'),
-- Eve likes Serpentina (already matched)
('550e8400-e29b-41d4-a716-446655440001', '550e8400-e29b-41d4-a716-446655440003', 'like', NOW() - INTERVAL '1 day 1 hour'),
-- Serpentina likes Eve (already matched)
('550e8400-e29b-41d4-a716-446655440003', '550e8400-e29b-41d4-a716-446655440001', 'like', NOW() - INTERVAL '1 day');

-- Update user online status
UPDATE users SET is_online = true, last_seen = NOW() 
WHERE id = '550e8400-e29b-41d4-a716-446655440001'; -- Eve online

UPDATE users SET is_online = false, last_seen = NOW() - INTERVAL '10 minutes' 
WHERE id = '550e8400-e29b-41d4-a716-446655440002'; -- Adam offline

UPDATE users SET is_online = true, last_seen = NOW() - INTERVAL '2 minutes' 
WHERE id = '550e8400-e29b-41d4-a716-446655440003'; -- Serpentina online
