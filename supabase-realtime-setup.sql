-- Enable real-time for tables
ALTER PUBLICATION supabase_realtime ADD TABLE messages;
ALTER PUBLICATION supabase_realtime ADD TABLE matches;
ALTER PUBLICATION supabase_realtime ADD TABLE users;

-- <PERSON>reate function to notify about new messages
CREATE OR R<PERSON>LACE FUNCTION notify_new_message()
RETURNS TRIGGER AS $$
BEGIN
  -- Notify both users in the match about the new message
  PERFORM pg_notify(
    'new_message',
    json_build_object(
      'match_id', NEW.match_id,
      'sender_id', NEW.sender_id,
      'message_id', NEW.id,
      'content', NEW.content,
      'type', NEW.type,
      'created_at', NEW.created_at
    )::text
  );
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for new messages
DROP TRIGGER IF EXISTS trigger_notify_new_message ON messages;
CREATE TRIGGER trigger_notify_new_message
  AFTER INSERT ON messages
  FOR EACH ROW
  EXECUTE FUNCTION notify_new_message();

-- <PERSON>reate function to update last_seen when user comes online
CREATE OR REPLACE FUNCTION update_last_seen()
<PERSON><PERSON><PERSON>NS TRIGGER AS $$
BEGIN
  IF NEW.is_online = true AND OLD.is_online = false THEN
    NEW.last_seen = NOW();
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for online status updates
DROP TRIGGER IF EXISTS trigger_update_last_seen ON users;
CREATE TRIGGER trigger_update_last_seen
  BEFORE UPDATE ON users
  FOR EACH ROW
  EXECUTE FUNCTION update_last_seen();

-- Create function to automatically create mutual matches
CREATE OR REPLACE FUNCTION check_mutual_match()
RETURNS TRIGGER AS $$
DECLARE
  mutual_match_exists BOOLEAN;
BEGIN
  -- Check if there's a mutual like (both users liked each other)
  SELECT EXISTS(
    SELECT 1 FROM user_interactions ui1
    JOIN user_interactions ui2 ON ui1.user_id = ui2.target_user_id 
                                AND ui1.target_user_id = ui2.user_id
    WHERE ui1.user_id = NEW.user_id 
      AND ui1.target_user_id = NEW.target_user_id
      AND ui1.interaction_type = 'like'
      AND ui2.interaction_type = 'like'
  ) INTO mutual_match_exists;
  
  -- If mutual match exists, create a match record
  IF mutual_match_exists THEN
    INSERT INTO matches (user1_id, user2_id, status)
    VALUES (NEW.user_id, NEW.target_user_id, 'matched')
    ON CONFLICT (user1_id, user2_id) DO NOTHING;
    
    -- Also try the reverse order in case of unique constraint
    INSERT INTO matches (user1_id, user2_id, status)
    VALUES (NEW.target_user_id, NEW.user_id, 'matched')
    ON CONFLICT (user1_id, user2_id) DO NOTHING;
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for mutual matches
DROP TRIGGER IF EXISTS trigger_check_mutual_match ON user_interactions;
CREATE TRIGGER trigger_check_mutual_match
  AFTER INSERT ON user_interactions
  FOR EACH ROW
  WHEN (NEW.interaction_type = 'like')
  EXECUTE FUNCTION check_mutual_match();

-- Create indexes for better real-time performance
CREATE INDEX IF NOT EXISTS idx_messages_match_id_created_at ON messages (match_id, created_at DESC);
CREATE INDEX IF NOT EXISTS idx_messages_sender_id ON messages (sender_id);
CREATE INDEX IF NOT EXISTS idx_users_is_online_last_seen ON users (is_online, last_seen DESC);
CREATE INDEX IF NOT EXISTS idx_matches_users ON matches (user1_id, user2_id, status);

-- Create function to get user's matches with latest message info
CREATE OR REPLACE FUNCTION get_user_matches_with_messages(user_uuid UUID)
RETURNS TABLE (
  match_id UUID,
  other_user_id UUID,
  other_user_name TEXT,
  other_user_photos TEXT[],
  other_user_is_online BOOLEAN,
  other_user_last_seen TIMESTAMPTZ,
  match_status TEXT,
  match_created_at TIMESTAMPTZ,
  match_updated_at TIMESTAMPTZ,
  latest_message_content TEXT,
  latest_message_type TEXT,
  latest_message_created_at TIMESTAMPTZ,
  unread_count BIGINT
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    m.id as match_id,
    CASE 
      WHEN m.user1_id = user_uuid THEN m.user2_id 
      ELSE m.user1_id 
    END as other_user_id,
    CASE 
      WHEN m.user1_id = user_uuid THEN u2.name 
      ELSE u1.name 
    END as other_user_name,
    CASE 
      WHEN m.user1_id = user_uuid THEN u2.photos 
      ELSE u1.photos 
    END as other_user_photos,
    CASE 
      WHEN m.user1_id = user_uuid THEN u2.is_online 
      ELSE u1.is_online 
    END as other_user_is_online,
    CASE 
      WHEN m.user1_id = user_uuid THEN u2.last_seen 
      ELSE u1.last_seen 
    END as other_user_last_seen,
    m.status as match_status,
    m.created_at as match_created_at,
    m.updated_at as match_updated_at,
    latest_msg.content as latest_message_content,
    latest_msg.type as latest_message_type,
    latest_msg.created_at as latest_message_created_at,
    COALESCE(unread.count, 0) as unread_count
  FROM matches m
  JOIN users u1 ON m.user1_id = u1.id
  JOIN users u2 ON m.user2_id = u2.id
  LEFT JOIN LATERAL (
    SELECT content, type, created_at
    FROM messages msg
    WHERE msg.match_id = m.id
    ORDER BY msg.created_at DESC
    LIMIT 1
  ) latest_msg ON true
  LEFT JOIN LATERAL (
    SELECT COUNT(*) as count
    FROM messages msg
    WHERE msg.match_id = m.id
      AND msg.sender_id != user_uuid
      AND msg.read_at IS NULL
  ) unread ON true
  WHERE (m.user1_id = user_uuid OR m.user2_id = user_uuid)
    AND m.status = 'matched'
  ORDER BY COALESCE(latest_msg.created_at, m.updated_at) DESC;
END;
$$ LANGUAGE plpgsql;

-- Grant necessary permissions
GRANT USAGE ON SCHEMA public TO anon, authenticated;
GRANT ALL ON ALL TABLES IN SCHEMA public TO anon, authenticated;
GRANT ALL ON ALL SEQUENCES IN SCHEMA public TO anon, authenticated;
GRANT EXECUTE ON ALL FUNCTIONS IN SCHEMA public TO anon, authenticated;

-- Enable real-time for the tables
ALTER TABLE messages REPLICA IDENTITY FULL;
ALTER TABLE matches REPLICA IDENTITY FULL;
ALTER TABLE users REPLICA IDENTITY FULL;
