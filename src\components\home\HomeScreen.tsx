import React, { useState } from 'react';
import Button from '../ui/Button';
import Card from '../ui/Card';
import Badge from '../ui/Badge';
import TemptationMeter from '../ui/TemptationMeter';
import { User } from '../../types';

interface HomeScreenProps {
  onNavigate: (screen: string) => void;
}

const HomeScreen: React.FC<HomeScreenProps> = ({ onNavigate }) => {
  const [currentUserIndex, setCurrentUserIndex] = useState(0);

  const sampleUsers: User[] = [
    {
      id: '1',
      name: 'Eve',
      age: 25,
      bio: 'First woman, still looking for my perfect match. Love long walks in gardens and trying new fruits 🍎',
      photos: ['https://images.unsplash.com/photo-1494790108755-2616b612b786?w=400'],
      location: { lat: 0, lng: 0 },
      temptationScore: 95,
      badges: ['Apple Lover', 'Garden Expert', 'First Woman'],
      isOnline: true
    },
    {
      id: '2',
      name: '<PERSON><PERSON>',
      age: 28,
      bio: 'Independent spirit who left paradise to find real love. Not afraid of a little rebellion 😈',
      photos: ['https://images.unsplash.com/photo-1534528741775-53994a69daeb?w=400'],
      location: { lat: 0, lng: 0 },
      temptationScore: 88,
      badges: ['Rebel', 'Independent', 'Night Owl'],
      isOnline: true
    },
    {
      id: '3',
      name: 'Sarah',
      age: 24,
      bio: 'Sweet as honey, dangerous as a serpent. Looking for someone to share forbidden fruits with 🐍',
      photos: ['https://images.unsplash.com/photo-1524504388940-b1c1722653e1?w=400'],
      location: { lat: 0, lng: 0 },
      temptationScore: 76,
      badges: ['Sweet Talker', 'Snake Charmer', 'Honey Lover'],
      isOnline: false
    }
  ];

  const currentUser = sampleUsers[currentUserIndex];

  const handleLike = () => {
    // Apple toss animation would go here
    setCurrentUserIndex((prev) => (prev + 1) % sampleUsers.length);
  };

  const handlePass = () => {
    setCurrentUserIndex((prev) => (prev + 1) % sampleUsers.length);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-eden-50 via-paradise-50 to-forbidden-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b border-eden-100 p-4">
        <div className="flex items-center justify-between max-w-md mx-auto">
          <button
            onClick={() => onNavigate('profile')}
            className="p-2 rounded-full hover:bg-gray-100 transition-colors"
          >
            <i className="bi bi-person-circle text-2xl text-gray-600"></i>
          </button>
          
          <div className="text-center">
            <h1 className="text-xl font-comic font-bold text-gray-800">
              🍎 Forbidden Match
            </h1>
            <p className="text-xs text-gray-500">Ready to sin a little?</p>
          </div>
          
          <button
            onClick={() => onNavigate('chat')}
            className="p-2 rounded-full hover:bg-gray-100 transition-colors relative"
          >
            <i className="bi bi-chat-heart text-2xl text-gray-600"></i>
            <div className="absolute -top-1 -right-1 w-5 h-5 bg-forbidden-500 rounded-full flex items-center justify-center">
              <span className="text-xs text-white font-bold">3</span>
            </div>
          </button>
        </div>
      </div>

      {/* Main Content */}
      <div className="p-4 max-w-md mx-auto">
        {/* User Card */}
        <Card className="mb-6 overflow-hidden relative">
          <div className="relative">
            <img
              src={currentUser.photos[0] || 'https://placehold.co/400x500/e5e7eb/6b7280?text=No+Photo'}
              alt={currentUser.name}
              className="w-full h-96 object-cover"
              crossOrigin="anonymous"
            />
            
            {/* Online indicator */}
            {currentUser.isOnline && (
              <div className="absolute top-4 right-4 flex items-center gap-2 bg-white/90 backdrop-blur-sm rounded-full px-3 py-1">
                <div className="w-2 h-2 bg-eden-500 rounded-full animate-pulse"></div>
                <span className="text-xs font-comic text-gray-700">Online</span>
              </div>
            )}

            {/* Gradient overlay */}
            <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/60 to-transparent h-32"></div>
            
            {/* User info overlay */}
            <div className="absolute bottom-4 left-4 right-4 text-white">
              <div className="flex items-center gap-2 mb-2">
                <h2 className="text-2xl font-comic font-bold">{currentUser.name}</h2>
                <span className="text-lg">{currentUser.age}</span>
              </div>
              
              <div className="mb-3">
                <TemptationMeter score={currentUser.temptationScore} size="md" />
              </div>
              
              <div className="flex flex-wrap gap-1 mb-2">
                {currentUser.badges.slice(0, 3).map((badge, index) => (
                  <Badge key={index} variant="apple" size="sm">
                    {badge}
                  </Badge>
                ))}
              </div>
            </div>
          </div>
          
          <div className="p-4">
            <p className="text-gray-600 font-inter text-sm leading-relaxed">
              {currentUser.bio}
            </p>
          </div>
        </Card>

        {/* Action Buttons */}
        <div className="flex justify-center gap-6 mb-6">
          <button
            onClick={handlePass}
            className="w-16 h-16 bg-white rounded-full shadow-lg border-2 border-gray-200 flex items-center justify-center hover:scale-110 transition-transform duration-200"
          >
            <i className="bi bi-x-lg text-2xl text-gray-500"></i>
          </button>
          
          <button
            onClick={handleLike}
            className="w-20 h-20 bg-gradient-to-r from-forbidden-500 to-forbidden-600 rounded-full shadow-lg flex items-center justify-center hover:scale-110 transition-transform duration-200 animate-pulse-slow"
          >
            <i className="bi bi-apple text-3xl text-white"></i>
          </button>
          
          <button
            onClick={() => onNavigate('map')}
            className="w-16 h-16 bg-white rounded-full shadow-lg border-2 border-eden-200 flex items-center justify-center hover:scale-110 transition-transform duration-200"
          >
            <i className="bi bi-geo-alt text-2xl text-eden-600"></i>
          </button>
        </div>

        {/* Quick Actions */}
        <div className="grid grid-cols-2 gap-4">
          <Button
            onClick={() => onNavigate('confession')}
            variant="paradise"
            className="w-full"
            icon="bi bi-tree"
          >
            Confession Tree
          </Button>
          
          <Button
            onClick={() => onNavigate('snakebot')}
            variant="secondary"
            className="w-full"
            icon="bi bi-robot"
          >
            Ask Snake 🐍
          </Button>
        </div>
      </div>

      {/* Floating decorations */}
      <div className="fixed inset-0 pointer-events-none overflow-hidden">
        <div className="absolute top-20 left-4 text-2xl animate-float opacity-30">🌿</div>
        <div className="absolute top-40 right-8 text-xl animate-float opacity-30" style={{ animationDelay: '1s' }}>🐍</div>
        <div className="absolute bottom-32 left-8 text-xl animate-float opacity-30" style={{ animationDelay: '2s' }}>🌳</div>
      </div>
    </div>
  );
};

export default HomeScreen;
