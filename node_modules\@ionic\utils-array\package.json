{"name": "@ionic/utils-array", "version": "2.1.5", "description": "Array utils", "main": "dist/index.js", "types": "dist/index.d.ts", "homepage": "https://ionicframework.com/", "author": "Ionic Team <<EMAIL>> (https://ionic.io)", "license": "MIT", "engines": {"node": ">=10.3.0"}, "files": ["dist/", "LICENSE", "README.md"], "repository": {"type": "git", "url": "https://github.com/ionic-team/ionic-cli.git"}, "bugs": {"url": "https://github.com/ionic-team/ionic-cli/issues"}, "scripts": {"clean": "<PERSON><PERSON><PERSON> dist", "lint": "true", "build": "npm run clean && tsc", "watch": "tsc -w --preserveWatchOutput", "test": "jest --maxWorkers=4", "prepublishOnly": "npm run build"}, "dependencies": {"debug": "^4.0.0", "tslib": "^2.0.1"}, "devDependencies": {"@types/debug": "^4.1.1", "@types/jest": "^26.0.10", "@types/node": "~10.17.13", "jest": "^26.4.2", "jest-cli": "^26.0.1", "lint-staged": "^10.0.2", "rimraf": "^3.0.0", "ts-jest": "~26.3.0", "typescript": "~4.0.2"}, "gitHead": "f85e6db0eba47c8d97181b52d69c687c100f93fb"}