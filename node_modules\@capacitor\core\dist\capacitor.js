/*! Capacitor: https://capacitorjs.com/ - MIT License */
var capacitorExports=function(e){"use strict";const t=(e=>e.CapacitorPlatforms=(e=>{const t=new Map;t.set("web",{name:"web"});const n=e.CapacitorPlatforms||{currentPlatform:{name:"web"},platforms:t};return n.addPlatform=(e,t)=>{n.platforms.set(e,t)},n.setPlatform=e=>{n.platforms.has(e)&&(n.currentPlatform=n.platforms.get(e))},n})(e))("undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:"undefined"!=typeof window?window:"undefined"!=typeof global?global:{}),n=t.addPlatform,r=t.setPlatform;var i;e.ExceptionCode=void 0,(i=e.ExceptionCode||(e.ExceptionCode={})).Unimplemented="UNIMPLEMENTED",i.Unavailable="UNAVAILABLE";class o extends Error{constructor(e,t,n){super(e),this.message=e,this.code=t,this.data=n}}const s=t=>{var n,r,i,s,a;const l=t.CapacitorCustomPlatform||null,d=t.Capacitor||{},c=d.Plugins=d.Plugins||{},u=t.CapacitorPlatforms,m=(null===(n=null==u?void 0:u.currentPlatform)||void 0===n?void 0:n.getPlatform)||(()=>null!==l?l.name:(e=>{var t,n;return(null==e?void 0:e.androidBridge)?"android":(null===(n=null===(t=null==e?void 0:e.webkit)||void 0===t?void 0:t.messageHandlers)||void 0===n?void 0:n.bridge)?"ios":"web"})(t)),p=(null===(r=null==u?void 0:u.currentPlatform)||void 0===r?void 0:r.isNativePlatform)||(()=>"web"!==m()),f=(null===(i=null==u?void 0:u.currentPlatform)||void 0===i?void 0:i.isPluginAvailable)||(e=>{const t=h.get(e);return!!(null==t?void 0:t.platforms.has(m()))||!!g(e)}),g=(null===(s=null==u?void 0:u.currentPlatform)||void 0===s?void 0:s.getPluginHeader)||(e=>{var t;return null===(t=d.PluginHeaders)||void 0===t?void 0:t.find((t=>t.name===e))}),h=new Map,w=(null===(a=null==u?void 0:u.currentPlatform)||void 0===a?void 0:a.registerPlugin)||((t,n={})=>{const r=h.get(t);if(r)return console.warn(`Capacitor plugin "${t}" already registered. Cannot register plugins twice.`),r.proxy;const i=m(),s=g(t);let a;const u=r=>{let c;const u=(...u)=>{const m=(async()=>(!a&&i in n?a=a="function"==typeof n[i]?await n[i]():n[i]:null!==l&&!a&&"web"in n&&(a=a="function"==typeof n.web?await n.web():n.web),a))().then((n=>{const a=((n,r)=>{var a,l;if(!s){if(n)return null===(l=n[r])||void 0===l?void 0:l.bind(n);throw new o(`"${t}" plugin is not implemented on ${i}`,e.ExceptionCode.Unimplemented)}{const e=null==s?void 0:s.methods.find((e=>r===e.name));if(e)return"promise"===e.rtype?e=>d.nativePromise(t,r.toString(),e):(e,n)=>d.nativeCallback(t,r.toString(),e,n);if(n)return null===(a=n[r])||void 0===a?void 0:a.bind(n)}})(n,r);if(a){const e=a(...u);return c=null==e?void 0:e.remove,e}throw new o(`"${t}.${r}()" is not implemented on ${i}`,e.ExceptionCode.Unimplemented)}));return"addListener"===r&&(m.remove=async()=>c()),m};return u.toString=()=>`${r.toString()}() { [capacitor code] }`,Object.defineProperty(u,"name",{value:r,writable:!1,configurable:!1}),u},p=u("addListener"),f=u("removeListener"),w=(e,t)=>{const n=p({eventName:e},t),r=async()=>{const r=await n;f({eventName:e,callbackId:r},t)},i=new Promise((e=>n.then((()=>e({remove:r})))));return i.remove=async()=>{console.warn("Using addListener() without 'await' is deprecated."),await r()},i},v=new Proxy({},{get(e,t){switch(t){case"$$typeof":return;case"toJSON":return()=>({});case"addListener":return s?w:p;case"removeListener":return f;default:return u(t)}}});return c[t]=v,h.set(t,{name:t,proxy:v,platforms:new Set([...Object.keys(n),...s?[i]:[]])}),v});return d.convertFileSrc||(d.convertFileSrc=e=>e),d.getPlatform=m,d.handleError=e=>t.console.error(e),d.isNativePlatform=p,d.isPluginAvailable=f,d.pluginMethodNoop=(e,t,n)=>Promise.reject(`${n} does not have an implementation of "${t}".`),d.registerPlugin=w,d.Exception=o,d.DEBUG=!!d.DEBUG,d.isLoggingEnabled=!!d.isLoggingEnabled,d.platform=d.getPlatform(),d.isNative=d.isNativePlatform(),d},a=(e=>e.Capacitor=s(e))("undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:"undefined"!=typeof window?window:"undefined"!=typeof global?global:{}),l=a.registerPlugin,d=a.Plugins;class c{constructor(e){this.listeners={},this.retainedEventArguments={},this.windowListeners={},e&&(console.warn(`Capacitor WebPlugin "${e.name}" config object was deprecated in v3 and will be removed in v4.`),this.config=e)}addListener(e,t){let n=!1;this.listeners[e]||(this.listeners[e]=[],n=!0),this.listeners[e].push(t);const r=this.windowListeners[e];r&&!r.registered&&this.addWindowListener(r),n&&this.sendRetainedArgumentsForEvent(e);return Promise.resolve({remove:async()=>this.removeListener(e,t)})}async removeAllListeners(){this.listeners={};for(const e in this.windowListeners)this.removeWindowListener(this.windowListeners[e]);this.windowListeners={}}notifyListeners(e,t,n){const r=this.listeners[e];if(r)r.forEach((e=>e(t)));else if(n){let n=this.retainedEventArguments[e];n||(n=[]),n.push(t),this.retainedEventArguments[e]=n}}hasListeners(e){return!!this.listeners[e].length}registerWindowListener(e,t){this.windowListeners[t]={registered:!1,windowEventName:e,pluginEventName:t,handler:e=>{this.notifyListeners(t,e)}}}unimplemented(t="not implemented"){return new a.Exception(t,e.ExceptionCode.Unimplemented)}unavailable(t="not available"){return new a.Exception(t,e.ExceptionCode.Unavailable)}async removeListener(e,t){const n=this.listeners[e];if(!n)return;const r=n.indexOf(t);this.listeners[e].splice(r,1),this.listeners[e].length||this.removeWindowListener(this.windowListeners[e])}addWindowListener(e){window.addEventListener(e.windowEventName,e.handler),e.registered=!0}removeWindowListener(e){e&&(window.removeEventListener(e.windowEventName,e.handler),e.registered=!1)}sendRetainedArgumentsForEvent(e){const t=this.retainedEventArguments[e];t&&(delete this.retainedEventArguments[e],t.forEach((t=>{this.notifyListeners(e,t)})))}}const u=l("WebView"),m=e=>encodeURIComponent(e).replace(/%(2[346B]|5E|60|7C)/g,decodeURIComponent).replace(/[()]/g,escape),p=e=>e.replace(/(%[\dA-F]{2})+/gi,decodeURIComponent);class f extends c{async getCookies(){const e=document.cookie,t={};return e.split(";").forEach((e=>{if(e.length<=0)return;let[n,r]=e.replace(/=/,"CAP_COOKIE").split("CAP_COOKIE");n=p(n).trim(),r=p(r).trim(),t[n]=r})),t}async setCookie(e){try{const t=m(e.key),n=m(e.value),r=`; expires=${(e.expires||"").replace("expires=","")}`,i=(e.path||"/").replace("path=",""),o=null!=e.url&&e.url.length>0?`domain=${e.url}`:"";document.cookie=`${t}=${n||""}${r}; path=${i}; ${o};`}catch(e){return Promise.reject(e)}}async deleteCookie(e){try{document.cookie=`${e.key}=; Max-Age=0`}catch(e){return Promise.reject(e)}}async clearCookies(){try{const e=document.cookie.split(";")||[];for(const t of e)document.cookie=t.replace(/^ +/,"").replace(/=.*/,`=;expires=${(new Date).toUTCString()};path=/`)}catch(e){return Promise.reject(e)}}async clearAllCookies(){try{await this.clearCookies()}catch(e){return Promise.reject(e)}}}const g=l("CapacitorCookies",{web:()=>new f}),h=(e,t={})=>{const n=Object.assign({method:e.method||"GET",headers:e.headers},t),r=((e={})=>{const t=Object.keys(e);return Object.keys(e).map((e=>e.toLocaleLowerCase())).reduce(((n,r,i)=>(n[r]=e[t[i]],n)),{})})(e.headers)["content-type"]||"";if("string"==typeof e.data)n.body=e.data;else if(r.includes("application/x-www-form-urlencoded")){const t=new URLSearchParams;for(const[n,r]of Object.entries(e.data||{}))t.set(n,r);n.body=t.toString()}else if(r.includes("multipart/form-data")||e.data instanceof FormData){const t=new FormData;if(e.data instanceof FormData)e.data.forEach(((e,n)=>{t.append(n,e)}));else for(const n of Object.keys(e.data))t.append(n,e.data[n]);n.body=t;const r=new Headers(n.headers);r.delete("content-type"),n.headers=r}else(r.includes("application/json")||"object"==typeof e.data)&&(n.body=JSON.stringify(e.data));return n};class w extends c{async request(e){const t=h(e,e.webFetchExtra),n=((e,t=!0)=>e?Object.entries(e).reduce(((e,n)=>{const[r,i]=n;let o,s;return Array.isArray(i)?(s="",i.forEach((e=>{o=t?encodeURIComponent(e):e,s+=`${r}=${o}&`})),s.slice(0,-1)):(o=t?encodeURIComponent(i):i,s=`${r}=${o}`),`${e}&${s}`}),"").substr(1):null)(e.params,e.shouldEncodeUrlParams),r=n?`${e.url}?${n}`:e.url,i=await fetch(r,t),o=i.headers.get("content-type")||"";let s,a,{responseType:l="text"}=i.ok?e:{};switch(o.includes("application/json")&&(l="json"),l){case"arraybuffer":case"blob":a=await i.blob(),s=await(async e=>new Promise(((t,n)=>{const r=new FileReader;r.onload=()=>{const e=r.result;t(e.indexOf(",")>=0?e.split(",")[1]:e)},r.onerror=e=>n(e),r.readAsDataURL(e)})))(a);break;case"json":s=await i.json();break;default:s=await i.text()}const d={};return i.headers.forEach(((e,t)=>{d[t]=e})),{data:s,headers:d,status:i.status,url:i.url}}async get(e){return this.request(Object.assign(Object.assign({},e),{method:"GET"}))}async post(e){return this.request(Object.assign(Object.assign({},e),{method:"POST"}))}async put(e){return this.request(Object.assign(Object.assign({},e),{method:"PUT"}))}async patch(e){return this.request(Object.assign(Object.assign({},e),{method:"PATCH"}))}async delete(e){return this.request(Object.assign(Object.assign({},e),{method:"DELETE"}))}}const v=l("CapacitorHttp",{web:()=>new w});return e.Capacitor=a,e.CapacitorCookies=g,e.CapacitorException=o,e.CapacitorHttp=v,e.CapacitorPlatforms=t,e.Plugins=d,e.WebPlugin=c,e.WebView=u,e.addPlatform=n,e.buildRequestInit=h,e.registerPlugin=l,e.registerWebPlugin=e=>((e,t)=>{var n;const r=t.config,i=e.Plugins;if(!(null==r?void 0:r.name))throw new Error('Capacitor WebPlugin is using the deprecated "registerWebPlugin()" function, but without the config. Please use "registerPlugin()" instead to register this web plugin."');console.warn(`Capacitor plugin "${r.name}" is using the deprecated "registerWebPlugin()" function`),i[r.name]&&!(null===(n=null==r?void 0:r.platforms)||void 0===n?void 0:n.includes(e.getPlatform()))||(i[r.name]=t)})(a,e),e.setPlatform=r,Object.defineProperty(e,"__esModule",{value:!0}),e}({});
//# sourceMappingURL=capacitor.js.map
