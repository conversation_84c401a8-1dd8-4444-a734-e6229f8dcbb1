import React, { useState } from 'react';
import Button from '../ui/Button';
import Card from '../ui/Card';
import Badge from '../ui/Badge';
import TemptationMeter from '../ui/TemptationMeter';

interface ProfileScreenProps {
  onNavigate: (screen: string) => void;
}

const ProfileScreen: React.FC<ProfileScreenProps> = ({ onNavigate }) => {
  const [isEditing, setIsEditing] = useState(false);

  const userProfile = {
    name: 'Adam',
    age: 26,
    bio: 'First man looking for his perfect Eve. Love gardening, trying new fruits, and long walks in paradise. Recently learned about clothing but still prefer the natural look 🌿',
    photos: [
      'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400',
      'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=400'
    ],
    temptationScore: 82,
    badges: ['Garden Expert', 'Apple Enthusiast', 'First Man', 'Nature Lover'],
    stats: {
      matches: 47,
      applesSent: 156,
      confessions: 23
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-eden-50 via-paradise-50 to-forbidden-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b border-eden-100 p-4">
        <div className="flex items-center justify-between max-w-md mx-auto">
          <button
            onClick={() => onNavigate('home')}
            className="p-2 rounded-full hover:bg-gray-100 transition-colors"
          >
            <i className="bi bi-arrow-left text-xl text-gray-600"></i>
          </button>
          
          <div className="text-center">
            <h1 className="text-xl font-comic font-bold text-gray-800">
              👤 My Profile
            </h1>
            <p className="text-xs text-gray-500">Your forbidden identity</p>
          </div>
          
          <button
            onClick={() => setIsEditing(!isEditing)}
            className="p-2 rounded-full hover:bg-gray-100 transition-colors"
          >
            <i className="bi bi-pencil text-xl text-gray-600"></i>
          </button>
        </div>
      </div>

      <div className="p-4 max-w-md mx-auto space-y-6">
        {/* Profile Photos */}
        <Card className="overflow-hidden">
          <div className="relative">
            <img
              src={userProfile.photos[0] || 'https://placehold.co/400x300/e5e7eb/6b7280?text=Add+Photo'}
              alt="Profile"
              className="w-full h-64 object-cover"
              crossOrigin="anonymous"
            />
            <div className="absolute bottom-4 left-4 right-4">
              <div className="bg-white/90 backdrop-blur-sm rounded-2xl p-4">
                <div className="flex items-center justify-between mb-2">
                  <h2 className="text-2xl font-comic font-bold text-gray-800">
                    {userProfile.name}, {userProfile.age}
                  </h2>
                  <div className="text-right">
                    <div className="text-sm font-comic text-gray-600 mb-1">Temptation Score</div>
                    <TemptationMeter score={userProfile.temptationScore} size="sm" showLabel={false} />
                  </div>
                </div>
              </div>
            </div>
            {isEditing && (
              <button className="absolute top-4 right-4 w-10 h-10 bg-white/90 rounded-full flex items-center justify-center">
                <i className="bi bi-camera text-gray-600"></i>
              </button>
            )}
          </div>
        </Card>

        {/* Stats */}
        <Card className="p-6">
          <h3 className="text-lg font-comic font-semibold text-gray-800 mb-4">
            Paradise Stats
          </h3>
          <div className="grid grid-cols-3 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-eden-600">{userProfile.stats.matches}</div>
              <div className="text-xs text-gray-500 font-comic">Matches</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-forbidden-600">{userProfile.stats.applesSent}</div>
              <div className="text-xs text-gray-500 font-comic">Apples Sent</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-paradise-600">{userProfile.stats.confessions}</div>
              <div className="text-xs text-gray-500 font-comic">Confessions</div>
            </div>
          </div>
        </Card>

        {/* Bio */}
        <Card className="p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-comic font-semibold text-gray-800">
              About Me
            </h3>
            {isEditing && (
              <button className="text-eden-600 hover:text-eden-700">
                <i className="bi bi-pencil"></i>
              </button>
            )}
          </div>
          {isEditing ? (
            <textarea
              defaultValue={userProfile.bio}
              className="w-full p-3 border border-gray-300 rounded-xl focus:border-eden-500 focus:outline-none font-inter text-sm resize-none"
              rows={4}
            />
          ) : (
            <p className="text-gray-600 font-inter text-sm leading-relaxed">
              {userProfile.bio}
            </p>
          )}
        </Card>

        {/* Badges */}
        <Card className="p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-comic font-semibold text-gray-800">
              My Badges
            </h3>
            {isEditing && (
              <button className="text-eden-600 hover:text-eden-700">
                <i className="bi bi-plus-circle"></i>
              </button>
            )}
          </div>
          <div className="flex flex-wrap gap-2">
            {userProfile.badges.map((badge, index) => (
              <Badge key={index} variant="apple">
                {badge}
                {isEditing && (
                  <button className="ml-1 text-forbidden-500 hover:text-forbidden-700">
                    <i className="bi bi-x text-xs"></i>
                  </button>
                )}
              </Badge>
            ))}
          </div>
        </Card>

        {/* Settings */}
        <Card className="p-6">
          <h3 className="text-lg font-comic font-semibold text-gray-800 mb-4">
            Garden Settings
          </h3>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="font-inter text-gray-700">Show me on map</span>
              <button className="w-12 h-6 bg-eden-500 rounded-full relative">
                <div className="w-5 h-5 bg-white rounded-full absolute right-0.5 top-0.5"></div>
              </button>
            </div>
            <div className="flex items-center justify-between">
              <span className="font-inter text-gray-700">Apple notifications</span>
              <button className="w-12 h-6 bg-eden-500 rounded-full relative">
                <div className="w-5 h-5 bg-white rounded-full absolute right-0.5 top-0.5"></div>
              </button>
            </div>
            <div className="flex items-center justify-between">
              <span className="font-inter text-gray-700">Snake wisdom alerts</span>
              <button className="w-12 h-6 bg-gray-300 rounded-full relative">
                <div className="w-5 h-5 bg-white rounded-full absolute left-0.5 top-0.5"></div>
              </button>
            </div>
          </div>
        </Card>

        {/* Action Buttons */}
        <div className="space-y-3">
          {isEditing ? (
            <div className="flex gap-3">
              <Button
                onClick={() => setIsEditing(false)}
                variant="secondary"
                className="flex-1"
              >
                Cancel
              </Button>
              <Button
                onClick={() => setIsEditing(false)}
                variant="primary"
                className="flex-1"
              >
                Save Changes
              </Button>
            </div>
          ) : (
            <>
              <Button
                onClick={() => onNavigate('confession')}
                variant="paradise"
                className="w-full"
                icon="bi bi-tree"
              >
                Visit Confession Tree
              </Button>
              
              <Button
                onClick={() => onNavigate('snakebot')}
                variant="secondary"
                className="w-full"
                icon="bi bi-robot"
              >
                Chat with Snake Bot
              </Button>
            </>
          )}
        </div>

        {/* Logout */}
        <div className="pt-4 border-t border-gray-200">
          <button className="w-full text-center text-forbidden-600 hover:text-forbidden-700 font-comic text-sm">
            Leave Paradise (Logout)
          </button>
        </div>
      </div>
    </div>
  );
};

export default ProfileScreen;
