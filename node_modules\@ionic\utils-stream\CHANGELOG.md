# Change Log

All notable changes to this project will be documented in this file.
See [Conventional Commits](https://conventionalcommits.org) for commit guidelines.

## [3.1.5](https://github.com/ionic-team/ionic-cli/compare/@ionic/utils-stream@3.1.4...@ionic/utils-stream@3.1.5) (2020-08-28)

**Note:** Version bump only for package @ionic/utils-stream





## [3.1.4](https://github.com/ionic-team/ionic-cli/compare/@ionic/utils-stream@3.1.3...@ionic/utils-stream@3.1.4) (2020-08-25)

**Note:** Version bump only for package @ionic/utils-stream





## [3.1.3](https://github.com/ionic-team/ionic-cli/compare/@ionic/utils-stream@3.1.2...@ionic/utils-stream@3.1.3) (2020-05-12)


### Bug Fixes

* pin tslib to avoid "Cannot set property pathExists" error ([689e1f0](https://github.com/ionic-team/ionic-cli/commit/689e1f038b907356ef855a067a76d4822e7072a8))





## [3.1.2](https://github.com/ionic-team/ionic-cli/compare/@ionic/utils-stream@3.1.1...@ionic/utils-stream@3.1.2) (2020-05-06)

**Note:** Version bump only for package @ionic/utils-stream





## [3.1.1](https://github.com/ionic-team/ionic-cli/compare/@ionic/utils-stream@3.1.0...@ionic/utils-stream@3.1.1) (2020-03-03)

**Note:** Version bump only for package @ionic/utils-stream





# 3.1.0 (2020-02-11)


### Features

* **start:** add new list starter option ([#4315](https://github.com/ionic-team/ionic-cli/issues/4315)) ([1df44c1](https://github.com/ionic-team/ionic-cli/commit/1df44c1591f37b89f2b672857740edd6cb2aea67))





## [3.0.2](https://github.com/ionic-team/ionic-cli/compare/@ionic/utils-stream@3.0.1...@ionic/utils-stream@3.0.2) (2020-02-10)

**Note:** Version bump only for package @ionic/utils-stream





## [3.0.1](https://github.com/ionic-team/ionic-cli/compare/@ionic/utils-stream@3.0.0...@ionic/utils-stream@3.0.1) (2020-02-03)

**Note:** Version bump only for package @ionic/utils-stream





# [3.0.0](https://github.com/ionic-team/ionic-cli/compare/@ionic/utils-stream@2.0.5...@ionic/utils-stream@3.0.0) (2020-01-25)


### chore

* require Node 10 ([5a47874](https://github.com/ionic-team/ionic-cli/commit/5a478746c074207b6dc96aa8771f04a606deb1ef))


### BREAKING CHANGES

* A minimum of Node.js 10.3.0 is required.





## [2.0.5](https://github.com/ionic-team/ionic-cli/compare/@ionic/utils-stream@2.0.4...@ionic/utils-stream@2.0.5) (2019-12-05)

**Note:** Version bump only for package @ionic/utils-stream





## [2.0.4](https://github.com/ionic-team/ionic-cli/compare/@ionic/utils-stream@2.0.3...@ionic/utils-stream@2.0.4) (2019-09-18)

**Note:** Version bump only for package @ionic/utils-stream





## [2.0.3](https://github.com/ionic-team/ionic-cli/compare/@ionic/utils-stream@2.0.2...@ionic/utils-stream@2.0.3) (2019-08-23)

**Note:** Version bump only for package @ionic/utils-stream





## [2.0.2](https://github.com/ionic-team/ionic-cli/compare/@ionic/utils-stream@2.0.1...@ionic/utils-stream@2.0.2) (2019-08-14)

**Note:** Version bump only for package @ionic/utils-stream





## [2.0.1](https://github.com/ionic-team/ionic-cli/compare/@ionic/utils-stream@2.0.0...@ionic/utils-stream@2.0.1) (2019-08-07)

**Note:** Version bump only for package @ionic/utils-stream





# [2.0.0](https://github.com/ionic-team/ionic-cli/compare/@ionic/utils-stream@1.0.1...@ionic/utils-stream@2.0.0) (2019-06-10)


### Code Refactoring

* remove combineStreams ([93f4ff2](https://github.com/ionic-team/ionic-cli/commit/93f4ff2))


### BREAKING CHANGES

* `combineStreams` has been removed. Use
`stream-combiner2` directly.





## [1.0.1](https://github.com/ionic-team/ionic-cli/compare/@ionic/utils-stream@1.0.0...@ionic/utils-stream@1.0.1) (2019-06-05)

**Note:** Version bump only for package @ionic/utils-stream





# [1.0.0](https://github.com/ionic-team/ionic-cli/compare/@ionic/utils-stream@0.0.1...@ionic/utils-stream@1.0.0) (2019-05-29)


### chore

* require Node 8 ([5670e68](https://github.com/ionic-team/ionic-cli/commit/5670e68))


### BREAKING CHANGES

* A minimum of Node.js 8.9.4 is required.





<a name="0.0.1"></a>
## 0.0.1 (2019-02-27)




**Note:** Version bump only for package @ionic/utils-stream
