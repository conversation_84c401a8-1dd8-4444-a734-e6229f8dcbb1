import { useEffect, useState, useCallback } from 'react';
import { supabase, Message, sendMessage, getMessages } from '../lib/supabase';
import { useAuth } from '../contexts/AuthContext';

interface UseRealTimeChatProps {
  matchId: string;
}

export const useRealTimeChat = ({ matchId }: UseRealTimeChatProps) => {
  const [messages, setMessages] = useState<Message[]>([]);
  const [loading, setLoading] = useState(true);
  const [sending, setSending] = useState(false);
  const { user } = useAuth();

  // Load initial messages
  useEffect(() => {
    const loadMessages = async () => {
      if (!matchId) return;
      
      setLoading(true);
      const { data, error } = await getMessages(matchId);
      
      if (!error && data) {
        setMessages(data);
      }
      
      setLoading(false);
    };

    loadMessages();
  }, [matchId]);

  // Set up real-time subscription
  useEffect(() => {
    if (!matchId) return;

    // Subscribe to new messages
    const channel = supabase
      .channel(`messages:${matchId}`)
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'messages',
          filter: `match_id=eq.${matchId}`
        },
        (payload) => {
          const newMessage = payload.new as Message;
          setMessages(prev => {
            // Avoid duplicates
            if (prev.some(msg => msg.id === newMessage.id)) {
              return prev;
            }
            return [...prev, newMessage];
          });
        }
      )
      .on(
        'postgres_changes',
        {
          event: 'UPDATE',
          schema: 'public',
          table: 'messages',
          filter: `match_id=eq.${matchId}`
        },
        (payload) => {
          const updatedMessage = payload.new as Message;
          setMessages(prev => 
            prev.map(msg => 
              msg.id === updatedMessage.id ? updatedMessage : msg
            )
          );
        }
      )
      .subscribe();

    return () => {
      supabase.removeChannel(channel);
    };
  }, [matchId]);

  // Send message function
  const sendNewMessage = useCallback(async (content: string, type: 'text' | 'image' | 'emoji' = 'text') => {
    if (!user || !matchId || !content.trim()) return;

    setSending(true);
    
    try {
      const { data, error } = await sendMessage(matchId, user.id, content.trim(), type);
      
      if (error) {
        console.error('Error sending message:', error);
        return false;
      }
      
      return true;
    } catch (error) {
      console.error('Error sending message:', error);
      return false;
    } finally {
      setSending(false);
    }
  }, [user, matchId]);

  // Mark messages as read
  const markAsRead = useCallback(async (messageIds: string[]) => {
    if (!user || messageIds.length === 0) return;

    try {
      await supabase
        .from('messages')
        .update({ read_at: new Date().toISOString() })
        .in('id', messageIds)
        .neq('sender_id', user.id); // Don't mark own messages as read
    } catch (error) {
      console.error('Error marking messages as read:', error);
    }
  }, [user]);

  // Get unread message count
  const unreadCount = messages.filter(
    msg => msg.sender_id !== user?.id && !msg.read_at
  ).length;

  return {
    messages,
    loading,
    sending,
    sendMessage: sendNewMessage,
    markAsRead,
    unreadCount
  };
};
