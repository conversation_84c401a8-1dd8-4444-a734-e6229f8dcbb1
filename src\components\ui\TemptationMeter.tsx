import React from 'react';

interface TemptationMeterProps {
  score: number;
  size?: 'sm' | 'md' | 'lg';
  showLabel?: boolean;
}

const TemptationMeter: React.FC<TemptationMeterProps> = ({
  score,
  size = 'md',
  showLabel = true
}) => {
  const sizeClasses = {
    sm: 'w-16 h-2',
    md: 'w-24 h-3',
    lg: 'w-32 h-4'
  };

  const getColor = (score: number) => {
    if (score >= 80) return 'bg-forbidden-500';
    if (score >= 60) return 'bg-paradise-500';
    if (score >= 40) return 'bg-eden-500';
    return 'bg-gray-400';
  };

  const getLabel = (score: number) => {
    if (score >= 90) return 'Irresistible 🔥';
    if (score >= 80) return 'Very Tempting 😈';
    if (score >= 60) return 'Quite Tempting 😏';
    if (score >= 40) return 'Mildly Tempting 😊';
    return 'Innocent 😇';
  };

  return (
    <div className="flex flex-col items-center gap-1">
      <div className={`bg-gray-200 rounded-full overflow-hidden ${sizeClasses[size]}`}>
        <div
          className={`h-full transition-all duration-500 ${getColor(score)}`}
          style={{ width: `${score}%` }}
        />
      </div>
      {showLabel && (
        <span className="text-xs font-comic text-gray-600">
          {getLabel(score)}
        </span>
      )}
    </div>
  );
};

export default TemptationMeter;
