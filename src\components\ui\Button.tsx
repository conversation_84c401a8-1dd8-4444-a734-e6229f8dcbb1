import React from 'react';

interface ButtonProps {
  children: React.ReactNode;
  onClick?: () => void;
  variant?: 'primary' | 'secondary' | 'forbidden' | 'paradise';
  size?: 'sm' | 'md' | 'lg';
  className?: string;
  disabled?: boolean;
  icon?: string;
}

const Button: React.FC<ButtonProps> = ({
  children,
  onClick,
  variant = 'primary',
  size = 'md',
  className = '',
  disabled = false,
  icon
}) => {
  const baseClasses = 'font-comic font-semibold rounded-full transition-all duration-300 transform hover:scale-105 active:scale-95 shadow-lg';
  
  const variantClasses = {
    primary: 'bg-gradient-to-r from-eden-500 to-eden-600 text-white hover:from-eden-600 hover:to-eden-700',
    secondary: 'bg-white text-eden-600 border-2 border-eden-500 hover:bg-eden-50',
    forbidden: 'bg-gradient-to-r from-forbidden-500 to-forbidden-600 text-white hover:from-forbidden-600 hover:to-forbidden-700',
    paradise: 'bg-gradient-to-r from-paradise-400 to-paradise-500 text-white hover:from-paradise-500 hover:to-paradise-600'
  };

  const sizeClasses = {
    sm: 'px-4 py-2 text-sm',
    md: 'px-6 py-3 text-base',
    lg: 'px-8 py-4 text-lg'
  };

  return (
    <button
      onClick={onClick}
      disabled={disabled}
      className={`${baseClasses} ${variantClasses[variant]} ${sizeClasses[size]} ${className} ${disabled ? 'opacity-50 cursor-not-allowed' : ''}`}
    >
      <div className="flex items-center justify-center gap-2">
        {icon && <i className={icon}></i>}
        {children}
      </div>
    </button>
  );
};

export default Button;
