export interface User {
  id: string;
  name: string;
  age: number;
  bio: string;
  photos: string[];
  location: {
    lat: number;
    lng: number;
  };
  temptationScore: number;
  badges: string[];
  isOnline: boolean;
  distance?: number;
}

export interface Match {
  id: string;
  user: User;
  timestamp: Date;
  isRead: boolean;
}

export interface Message {
  id: string;
  senderId: string;
  content: string;
  timestamp: Date;
  type: 'text' | 'apple' | 'sticker';
}

export interface OnboardingData {
  name: string;
  age: number;
  bio: string;
  photos: string[];
  appleQuestion: string;
  temptationLevel: number;
  leafCoverPreference: string;
}
