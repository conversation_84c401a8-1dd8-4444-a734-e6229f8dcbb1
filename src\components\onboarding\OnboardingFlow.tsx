import React, { useState } from 'react';
import Button from '../ui/Button';
import Card from '../ui/Card';
import { OnboardingData } from '../../types';

interface OnboardingFlowProps {
  onComplete: (data: OnboardingData) => void;
}

const OnboardingFlow: React.FC<OnboardingFlowProps> = ({ onComplete }) => {
  const [step, setStep] = useState(0);
  const [data, setData] = useState<Partial<OnboardingData>>({});

  const questions = [
    {
      title: "What's your tempting name?",
      subtitle: "How should other forbidden souls call you?",
      type: "text",
      key: "name",
      placeholder: "Enter your name..."
    },
    {
      title: "How many years of temptation?",
      subtitle: "Age is just a number in paradise",
      type: "number",
      key: "age",
      placeholder: "Your age..."
    },
    {
      title: "Tell us about your forbidden side",
      subtitle: "What makes you irresistible?",
      type: "textarea",
      key: "bio",
      placeholder: "I'm the kind of person who would..."
    },
    {
      title: "How many apples would you trade for love?",
      subtitle: "This is very important for compatibility",
      type: "select",
      key: "appleQuestion",
      options: [
        "Just one bite 🍎",
        "The whole orchard 🌳",
        "I'd plant new trees 🌱",
        "Apples are overrated, I prefer figs 🌿"
      ]
    },
    {
      title: "On a scale of 1-10, how tempting are you?",
      subtitle: "Be honest, the snake is watching 🐍",
      type: "slider",
      key: "temptationLevel",
      min: 1,
      max: 10
    },
    {
      title: "Are you a leaf-cover kind of person?",
      subtitle: "Fashion choices matter in paradise",
      type: "select",
      key: "leafCoverPreference",
      options: [
        "Fig leaves only, classic style 🌿",
        "Modern minimalist, barely there 😏",
        "Full coverage, I'm shy 😊",
        "Depends on the weather 🌤️"
      ]
    }
  ];

  const currentQuestion = questions[step];

  const handleNext = () => {
    if (step < questions.length - 1) {
      setStep(step + 1);
    } else {
      onComplete(data as OnboardingData);
    }
  };

  const handleInputChange = (value: any) => {
    setData({ ...data, [currentQuestion.key]: value });
  };

  const renderInput = () => {
    switch (currentQuestion.type) {
      case "text":
      case "number":
        return (
          <input
            type={currentQuestion.type}
            placeholder={currentQuestion.placeholder}
            value={data[currentQuestion.key as keyof OnboardingData] || ''}
            onChange={(e) => handleInputChange(e.target.value)}
            className="w-full px-4 py-3 rounded-xl border border-gray-300 focus:border-eden-500 focus:outline-none font-inter text-center"
          />
        );
      
      case "textarea":
        return (
          <textarea
            placeholder={currentQuestion.placeholder}
            value={data[currentQuestion.key as keyof OnboardingData] || ''}
            onChange={(e) => handleInputChange(e.target.value)}
            rows={4}
            className="w-full px-4 py-3 rounded-xl border border-gray-300 focus:border-eden-500 focus:outline-none font-inter resize-none"
          />
        );
      
      case "select":
        return (
          <div className="space-y-3">
            {currentQuestion.options?.map((option, index) => (
              <button
                key={index}
                onClick={() => handleInputChange(option)}
                className={`w-full p-4 rounded-xl border-2 transition-all duration-300 font-comic text-left ${
                  data[currentQuestion.key as keyof OnboardingData] === option
                    ? 'border-eden-500 bg-eden-50 text-eden-700'
                    : 'border-gray-200 hover:border-eden-300 hover:bg-gray-50'
                }`}
              >
                {option}
              </button>
            ))}
          </div>
        );
      
      case "slider":
        return (
          <div className="space-y-4">
            <input
              type="range"
              min={currentQuestion.min}
              max={currentQuestion.max}
              value={data[currentQuestion.key as keyof OnboardingData] || 5}
              onChange={(e) => handleInputChange(parseInt(e.target.value))}
              className="w-full h-3 bg-gray-200 rounded-lg appearance-none cursor-pointer slider"
            />
            <div className="text-center">
              <span className="text-2xl font-comic text-eden-600">
                {data[currentQuestion.key as keyof OnboardingData] || 5}/10
              </span>
              <div className="text-sm text-gray-500 mt-1">
                {(data[currentQuestion.key as keyof OnboardingData] as number) >= 8 ? "🔥 Irresistible" :
                 (data[currentQuestion.key as keyof OnboardingData] as number) >= 6 ? "😈 Very Tempting" :
                 (data[currentQuestion.key as keyof OnboardingData] as number) >= 4 ? "😏 Quite Tempting" : "😇 Innocent"}
              </div>
            </div>
          </div>
        );
      
      default:
        return null;
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-eden-50 via-paradise-50 to-forbidden-50 flex items-center justify-center p-4">
      <Card className="w-full max-w-lg p-8">
        <div className="mb-8">
          <div className="flex justify-between items-center mb-4">
            <span className="text-sm font-inter text-gray-500">
              Question {step + 1} of {questions.length}
            </span>
            <div className="flex space-x-1">
              {questions.map((_, index) => (
                <div
                  key={index}
                  className={`w-2 h-2 rounded-full ${
                    index <= step ? 'bg-eden-500' : 'bg-gray-200'
                  }`}
                />
              ))}
            </div>
          </div>
          
          <h2 className="text-2xl font-comic font-bold text-gray-800 mb-2">
            {currentQuestion.title}
          </h2>
          <p className="text-gray-600 font-inter">
            {currentQuestion.subtitle}
          </p>
        </div>

        <div className="mb-8">
          {renderInput()}
        </div>

        <div className="flex gap-4">
          {step > 0 && (
            <Button
              onClick={() => setStep(step - 1)}
              variant="secondary"
              className="flex-1"
            >
              Back
            </Button>
          )}
          <Button
            onClick={handleNext}
            variant={step === questions.length - 1 ? "forbidden" : "primary"}
            className="flex-1"
            disabled={!data[currentQuestion.key as keyof OnboardingData]}
          >
            {step === questions.length - 1 ? "Enter Paradise" : "Next"}
          </Button>
        </div>
      </Card>
    </div>
  );
};

export default OnboardingFlow;
