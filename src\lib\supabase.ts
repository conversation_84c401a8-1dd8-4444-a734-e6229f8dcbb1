import { createClient } from '@supabase/supabase-js'

const supabaseUrl = 'https://ilomqtlpbamcmslascdl.supabase.co'
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imlsb21xdGxwYmFtY21zbGFzY2RsIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDYxMjIyMDEsImV4cCI6MjA2MTY5ODIwMX0.U84Mng2d8Vl2pNs4p0nmEPsIu4RKeXVoVKkR0gyGRcc'

export const supabase = createClient(supabaseUrl, supabaseAnonKey)

// Database types
export interface User {
  id: string
  email: string
  name: string
  age: number
  bio: string
  photos: string[]
  location?: {
    latitude: number
    longitude: number
    city?: string
  }
  preferences: {
    ageRange: [number, number]
    maxDistance: number
    interests: string[]
  }
  isOnline: boolean
  lastSeen: string
  created_at: string
  updated_at: string
}

export interface Match {
  id: string
  user1_id: string
  user2_id: string
  status: 'pending' | 'matched' | 'rejected'
  created_at: string
  updated_at: string
}

export interface Message {
  id: string
  match_id: string
  sender_id: string
  content: string
  type: 'text' | 'image' | 'emoji'
  created_at: string
  read_at?: string
}

export interface Confession {
  id: string
  user_id: string
  content: string
  is_anonymous: boolean
  likes: number
  created_at: string
}

// Auth functions
export const signUp = async (email: string, password: string, userData: Partial<User>) => {
  const { data, error } = await supabase.auth.signUp({
    email,
    password,
    options: {
      data: userData
    }
  })
  return { data, error }
}

export const signIn = async (email: string, password: string) => {
  const { data, error } = await supabase.auth.signInWithPassword({
    email,
    password
  })
  return { data, error }
}

export const signOut = async () => {
  const { error } = await supabase.auth.signOut()
  return { error }
}

export const getCurrentUser = async () => {
  const { data: { user } } = await supabase.auth.getUser()
  return user
}

// User functions
export const createUserProfile = async (userId: string, profileData: Partial<User>) => {
  const { data, error } = await supabase
    .from('users')
    .insert([{ id: userId, ...profileData }])
    .select()
  return { data, error }
}

export const updateUserProfile = async (userId: string, updates: Partial<User>) => {
  const { data, error } = await supabase
    .from('users')
    .update(updates)
    .eq('id', userId)
    .select()
  return { data, error }
}

export const getUserProfile = async (userId: string) => {
  const { data, error } = await supabase
    .from('users')
    .select('*')
    .eq('id', userId)
    .single()
  return { data, error }
}

// Matching functions
export const getPotentialMatches = async (userId: string, limit = 10) => {
  const { data, error } = await supabase
    .from('users')
    .select('*')
    .neq('id', userId)
    .limit(limit)
  return { data, error }
}

export const createMatch = async (user1Id: string, user2Id: string, status: 'pending' | 'matched' | 'rejected') => {
  const { data, error } = await supabase
    .from('matches')
    .insert([{ user1_id: user1Id, user2_id: user2Id, status }])
    .select()
  return { data, error }
}

export const getUserMatches = async (userId: string) => {
  const { data, error } = await supabase
    .from('matches')
    .select(`
      *,
      user1:users!matches_user1_id_fkey(*),
      user2:users!matches_user2_id_fkey(*)
    `)
    .or(`user1_id.eq.${userId},user2_id.eq.${userId}`)
    .eq('status', 'matched')
  return { data, error }
}

// Messaging functions
export const sendMessage = async (matchId: string, senderId: string, content: string, type: 'text' | 'image' | 'emoji' = 'text') => {
  const { data, error } = await supabase
    .from('messages')
    .insert([{ match_id: matchId, sender_id: senderId, content, type }])
    .select()
  return { data, error }
}

export const getMessages = async (matchId: string) => {
  const { data, error } = await supabase
    .from('messages')
    .select('*')
    .eq('match_id', matchId)
    .order('created_at', { ascending: true })
  return { data, error }
}

// Confession functions
export const createConfession = async (userId: string, content: string, isAnonymous = true) => {
  const { data, error } = await supabase
    .from('confessions')
    .insert([{ user_id: userId, content, is_anonymous: isAnonymous }])
    .select()
  return { data, error }
}

export const getConfessions = async (limit = 20) => {
  const { data, error } = await supabase
    .from('confessions')
    .select('*')
    .order('created_at', { ascending: false })
    .limit(limit)
  return { data, error }
}
