-- <PERSON><PERSON> <PERSON> <PERSON> - Forbidden Match Database Schema

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "postgis";

-- Users table
CREATE TABLE IF NOT EXISTS users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    email VARCHAR(255) UNIQUE NOT NULL,
    name VARCHAR(100) NOT NULL,
    age INTEGER NOT NULL CHECK (age >= 18),
    bio TEXT,
    photos TEXT[] DEFAULT '{}',
    location JSONB,
    preferences JSONB DEFAULT '{}',
    is_online BOOLEAN DEFAULT false,
    last_seen TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Matches table
CREATE TABLE IF NOT EXISTS matches (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user1_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    user2_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'matched', 'rejected')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user1_id, user2_id)
);

-- Messages table
CREATE TABLE IF NOT EXISTS messages (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    match_id UUID NOT NULL REFERENCES matches(id) ON DELETE CASCADE,
    sender_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    content TEXT NOT NULL,
    type VARCHAR(20) DEFAULT 'text' CHECK (type IN ('text', 'image', 'emoji')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    read_at TIMESTAMP WITH TIME ZONE
);

-- Confessions table (for the Confession Tree feature)
CREATE TABLE IF NOT EXISTS confessions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    content TEXT NOT NULL,
    is_anonymous BOOLEAN DEFAULT true,
    likes INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- User interactions table (likes, passes, etc.)
CREATE TABLE IF NOT EXISTS user_interactions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    target_user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    interaction_type VARCHAR(20) NOT NULL CHECK (interaction_type IN ('like', 'pass', 'super_like', 'block')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id, target_user_id)
);

-- Reports table (for user safety)
CREATE TABLE IF NOT EXISTS reports (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    reporter_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    reported_user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    reason VARCHAR(100) NOT NULL,
    description TEXT,
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'reviewed', 'resolved')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes for better performance
CREATE INDEX IF NOT EXISTS idx_users_location ON users USING GIN (location);
CREATE INDEX IF NOT EXISTS idx_users_age ON users (age);
CREATE INDEX IF NOT EXISTS idx_users_is_online ON users (is_online);
CREATE INDEX IF NOT EXISTS idx_matches_user1_id ON matches (user1_id);
CREATE INDEX IF NOT EXISTS idx_matches_user2_id ON matches (user2_id);
CREATE INDEX IF NOT EXISTS idx_matches_status ON matches (status);
CREATE INDEX IF NOT EXISTS idx_messages_match_id ON messages (match_id);
CREATE INDEX IF NOT EXISTS idx_messages_created_at ON messages (created_at);
CREATE INDEX IF NOT EXISTS idx_confessions_created_at ON confessions (created_at);
CREATE INDEX IF NOT EXISTS idx_user_interactions_user_id ON user_interactions (user_id);
CREATE INDEX IF NOT EXISTS idx_user_interactions_target_user_id ON user_interactions (target_user_id);

-- Functions for updated_at timestamps
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Triggers for updated_at
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_matches_updated_at BEFORE UPDATE ON matches FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Row Level Security (RLS) policies
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE matches ENABLE ROW LEVEL SECURITY;
ALTER TABLE messages ENABLE ROW LEVEL SECURITY;
ALTER TABLE confessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_interactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE reports ENABLE ROW LEVEL SECURITY;

-- Users can only see and edit their own profile
CREATE POLICY "Users can view their own profile" ON users FOR SELECT USING (auth.uid() = id);
CREATE POLICY "Users can update their own profile" ON users FOR UPDATE USING (auth.uid() = id);
CREATE POLICY "Users can insert their own profile" ON users FOR INSERT WITH CHECK (auth.uid() = id);

-- Users can see other users for matching (but not sensitive data)
CREATE POLICY "Users can view other profiles for matching" ON users FOR SELECT USING (true);

-- Match policies
CREATE POLICY "Users can view their matches" ON matches FOR SELECT USING (auth.uid() = user1_id OR auth.uid() = user2_id);
CREATE POLICY "Users can create matches" ON matches FOR INSERT WITH CHECK (auth.uid() = user1_id);
CREATE POLICY "Users can update their matches" ON matches FOR UPDATE USING (auth.uid() = user1_id OR auth.uid() = user2_id);

-- Message policies
CREATE POLICY "Users can view messages in their matches" ON messages FOR SELECT USING (
    EXISTS (
        SELECT 1 FROM matches 
        WHERE matches.id = messages.match_id 
        AND (matches.user1_id = auth.uid() OR matches.user2_id = auth.uid())
    )
);
CREATE POLICY "Users can send messages in their matches" ON messages FOR INSERT WITH CHECK (
    auth.uid() = sender_id AND
    EXISTS (
        SELECT 1 FROM matches 
        WHERE matches.id = match_id 
        AND (matches.user1_id = auth.uid() OR matches.user2_id = auth.uid())
        AND matches.status = 'matched'
    )
);

-- Confession policies (anonymous viewing, but users can only create their own)
CREATE POLICY "Anyone can view confessions" ON confessions FOR SELECT USING (true);
CREATE POLICY "Users can create confessions" ON confessions FOR INSERT WITH CHECK (auth.uid() = user_id);

-- User interaction policies
CREATE POLICY "Users can view their interactions" ON user_interactions FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can create interactions" ON user_interactions FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Report policies
CREATE POLICY "Users can create reports" ON reports FOR INSERT WITH CHECK (auth.uid() = reporter_id);
CREATE POLICY "Users can view their own reports" ON reports FOR SELECT USING (auth.uid() = reporter_id);

-- Sample data for testing
INSERT INTO users (id, email, name, age, bio, photos, location, preferences, is_online) VALUES
(
    '550e8400-e29b-41d4-a716-************',
    '<EMAIL>',
    'Eve',
    25,
    'Looking for my forbidden fruit partner 🍎 Love adventures and deep conversations.',
    ARRAY['https://images.unsplash.com/photo-1494790108755-2616b612b786?w=400'],
    '{"latitude": 40.7128, "longitude": -74.0060, "city": "New York"}',
    '{"ageRange": [22, 35], "maxDistance": 50, "interests": ["adventure", "books", "travel"]}',
    true
),
(
    '550e8400-e29b-41d4-a716-************',
    '<EMAIL>',
    'Adam',
    28,
    'Searching for my other half in this digital paradise 🌳 Passionate about life and love.',
    ARRAY['https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400'],
    '{"latitude": 40.7589, "longitude": -73.9851, "city": "New York"}',
    '{"ageRange": [20, 30], "maxDistance": 25, "interests": ["music", "art", "nature"]}',
    true
),
(
    '550e8400-e29b-41d4-a716-446655440003',
    '<EMAIL>',
    'Serpentina',
    26,
    'Wise beyond my years, here to tempt you with good conversation 🐍✨',
    ARRAY['https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=400'],
    '{"latitude": 40.7505, "longitude": -73.9934, "city": "New York"}',
    '{"ageRange": [24, 32], "maxDistance": 30, "interests": ["philosophy", "psychology", "mystery"]}',
    false
);

-- Sample confessions
INSERT INTO confessions (user_id, content, is_anonymous) VALUES
('550e8400-e29b-41d4-a716-************', 'Sometimes I wonder if true love really exists in this digital age...', true),
('550e8400-e29b-41d4-a716-************', 'I believe everyone deserves a second chance at love, even in paradise.', true),
('550e8400-e29b-41d4-a716-446655440003', 'The most forbidden fruit is often the sweetest. 🍎', true);
