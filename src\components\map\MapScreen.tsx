import React, { useState } from 'react';
import Button from '../ui/Button';
import Card from '../ui/Card';
import Badge from '../ui/Badge';
import TemptationMeter from '../ui/TemptationMeter';
import { User } from '../../types';

interface MapScreenProps {
  onNavigate: (screen: string) => void;
}

const MapScreen: React.FC<MapScreenProps> = ({ onNavigate }) => {
  const [selectedFilter, setSelectedFilter] = useState('all');
  const [selectedUser, setSelectedUser] = useState<User | null>(null);

  const nearbyUsers: User[] = [
    {
      id: '1',
      name: 'Eve',
      age: 25,
      bio: 'First woman, still looking for my perfect match',
      photos: ['https://images.unsplash.com/photo-1494790108755-2616b612b786?w=400'],
      location: { lat: 40.7128, lng: -74.0060 },
      temptationScore: 95,
      badges: ['<PERSON> Lover', '<PERSON> Expert'],
      isOnline: true,
      distance: 0.5
    },
    {
      id: '2',
      name: '<PERSON><PERSON>',
      age: 28,
      bio: 'Independent spirit who left paradise',
      photos: ['https://images.unsplash.com/photo-1534528741775-53994a69daeb?w=400'],
      location: { lat: 40.7589, lng: -73.9851 },
      temptationScore: 88,
      badges: ['Rebel', 'Night Owl'],
      isOnline: true,
      distance: 1.2
    },
    {
      id: '3',
      name: 'Sarah',
      age: 24,
      bio: 'Sweet as honey, dangerous as a serpent',
      photos: ['https://images.unsplash.com/photo-1524504388940-b1c1722653e1?w=400'],
      location: { lat: 40.6892, lng: -74.0445 },
      temptationScore: 76,
      badges: ['Snake Charmer', 'Sweet Talker'],
      isOnline: false,
      distance: 2.1
    }
  ];

  const filters = [
    { id: 'all', label: 'All Souls', icon: 'bi bi-people' },
    { id: 'eves', label: 'Only Eves', icon: 'bi bi-person-dress' },
    { id: 'adams', label: 'Only Adams', icon: 'bi bi-person' },
    { id: 'snake-charmers', label: 'Snake Charmers', icon: 'bi bi-bug' },
    { id: 'apple-biters', label: 'Apple Biters', icon: 'bi bi-apple' },
    { id: 'leaf-lovers', label: 'Leaf Lovers', icon: 'bi bi-tree' }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-eden-50 via-paradise-50 to-forbidden-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b border-eden-100 p-4">
        <div className="flex items-center justify-between max-w-md mx-auto">
          <button
            onClick={() => onNavigate('home')}
            className="p-2 rounded-full hover:bg-gray-100 transition-colors"
          >
            <i className="bi bi-arrow-left text-xl text-gray-600"></i>
          </button>
          
          <div className="text-center">
            <h1 className="text-xl font-comic font-bold text-gray-800">
              🗺️ Garden Map
            </h1>
            <p className="text-xs text-gray-500">Find souls near you</p>
          </div>
          
          <button className="p-2 rounded-full hover:bg-gray-100 transition-colors">
            <i className="bi bi-funnel text-xl text-gray-600"></i>
          </button>
        </div>
      </div>

      {/* Filters */}
      <div className="p-4">
        <div className="flex gap-2 overflow-x-auto pb-2">
          {filters.map((filter) => (
            <button
              key={filter.id}
              onClick={() => setSelectedFilter(filter.id)}
              className={`flex items-center gap-2 px-4 py-2 rounded-full whitespace-nowrap font-comic text-sm transition-all ${
                selectedFilter === filter.id
                  ? 'bg-eden-500 text-white'
                  : 'bg-white text-gray-600 hover:bg-eden-50'
              }`}
            >
              <i className={filter.icon}></i>
              {filter.label}
            </button>
          ))}
        </div>
      </div>

      {/* Map Area */}
      <div className="px-4 mb-4">
        <Card className="h-64 relative overflow-hidden">
          {/* Simulated map background */}
          <div className="absolute inset-0 bg-gradient-to-br from-eden-100 to-eden-200">
            {/* Map decorations */}
            <div className="absolute inset-0">
              <div className="absolute top-4 left-4 text-2xl">🌳</div>
              <div className="absolute top-8 right-8 text-xl">🌿</div>
              <div className="absolute bottom-8 left-8 text-xl">🌊</div>
              <div className="absolute bottom-4 right-4 text-2xl">🏔️</div>
              <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 text-3xl">🌳</div>
            </div>
            
            {/* User avatars on map */}
            {nearbyUsers.map((user, index) => (
              <button
                key={user.id}
                onClick={() => setSelectedUser(user)}
                className={`absolute w-12 h-12 rounded-full border-4 border-white shadow-lg hover:scale-110 transition-transform ${
                  user.isOnline ? 'ring-2 ring-eden-500' : ''
                }`}
                style={{
                  top: `${20 + index * 25}%`,
                  left: `${30 + index * 20}%`
                }}
              >
                <img
                  src={user.photos[0] || 'https://placehold.co/48x48/e5e7eb/6b7280?text=?'}
                  alt={user.name}
                  className="w-full h-full rounded-full object-cover"
                  crossOrigin="anonymous"
                />
                {user.isOnline && (
                  <div className="absolute -top-1 -right-1 w-4 h-4 bg-eden-500 rounded-full border-2 border-white"></div>
                )}
              </button>
            ))}
          </div>
        </Card>
      </div>

      {/* User List */}
      <div className="px-4 space-y-3">
        {nearbyUsers.map((user) => (
          <Card
            key={user.id}
            hover
            onClick={() => setSelectedUser(user)}
            className="p-4"
          >
            <div className="flex items-center gap-4">
              <div className="relative">
                <img
                  src={user.photos[0] || 'https://placehold.co/60x60/e5e7eb/6b7280?text=?'}
                  alt={user.name}
                  className="w-15 h-15 rounded-full object-cover"
                  crossOrigin="anonymous"
                />
                {user.isOnline && (
                  <div className="absolute -bottom-1 -right-1 w-4 h-4 bg-eden-500 rounded-full border-2 border-white"></div>
                )}
              </div>
              
              <div className="flex-1">
                <div className="flex items-center gap-2 mb-1">
                  <h3 className="font-comic font-semibold text-gray-800">{user.name}</h3>
                  <span className="text-gray-500">{user.age}</span>
                  <span className="text-xs text-gray-400">• {user.distance}km away</span>
                </div>
                
                <div className="mb-2">
                  <TemptationMeter score={user.temptationScore} size="sm" showLabel={false} />
                </div>
                
                <div className="flex gap-1">
                  {user.badges.slice(0, 2).map((badge, index) => (
                    <Badge key={index} variant="leaf" size="sm">
                      {badge}
                    </Badge>
                  ))}
                </div>
              </div>
              
              <div className="text-right">
                <i className="bi bi-chevron-right text-gray-400"></i>
              </div>
            </div>
          </Card>
        ))}
      </div>

      {/* User Detail Modal */}
      {selectedUser && (
        <div className="fixed inset-0 bg-black/50 flex items-end justify-center z-50">
          <Card className="w-full max-w-md m-4 mb-0 rounded-t-3xl rounded-b-none max-h-96 overflow-y-auto">
            <div className="p-6">
              <div className="flex justify-between items-start mb-4">
                <div className="flex items-center gap-3">
                  <img
                    src={selectedUser.photos[0] || 'https://placehold.co/60x60/e5e7eb/6b7280?text=?'}
                    alt={selectedUser.name}
                    className="w-16 h-16 rounded-full object-cover"
                    crossOrigin="anonymous"
                  />
                  <div>
                    <h3 className="text-xl font-comic font-bold text-gray-800">
                      {selectedUser.name}, {selectedUser.age}
                    </h3>
                    <p className="text-sm text-gray-500">
                      {selectedUser.distance}km away • {selectedUser.isOnline ? 'Online now' : 'Last seen recently'}
                    </p>
                  </div>
                </div>
                <button
                  onClick={() => setSelectedUser(null)}
                  className="p-2 rounded-full hover:bg-gray-100"
                >
                  <i className="bi bi-x-lg text-gray-500"></i>
                </button>
              </div>
              
              <div className="mb-4">
                <TemptationMeter score={selectedUser.temptationScore} size="lg" />
              </div>
              
              <p className="text-gray-600 font-inter text-sm mb-4">
                {selectedUser.bio}
              </p>
              
              <div className="flex flex-wrap gap-2 mb-6">
                {selectedUser.badges.map((badge, index) => (
                  <Badge key={index} variant="apple">
                    {badge}
                  </Badge>
                ))}
              </div>
              
              <div className="flex gap-3">
                <Button variant="secondary" className="flex-1">
                  <i className="bi bi-chat-dots mr-2"></i>
                  Message
                </Button>
                <Button variant="forbidden" className="flex-1">
                  <i className="bi bi-apple mr-2"></i>
                  Toss Apple
                </Button>
              </div>
              
              <p className="text-xs text-center text-gray-500 mt-4 font-comic">
                "This {selectedUser.name} is hiding behind the digital bush 🌿"
              </p>
            </div>
          </Card>
        </div>
      )}
    </div>
  );
};

export default MapScreen;
