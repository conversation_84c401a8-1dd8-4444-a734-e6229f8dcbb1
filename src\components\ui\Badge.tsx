import React from 'react';

interface BadgeProps {
  children: React.ReactNode;
  variant?: 'apple' | 'snake' | 'leaf' | 'default';
  size?: 'sm' | 'md';
  className?: string;
}

const Badge: React.FC<BadgeProps> = ({
  children,
  variant = 'default',
  size = 'md',
  className = ''
}) => {
  const baseClasses = 'inline-flex items-center gap-1 font-comic font-medium rounded-full';
  
  const variantClasses = {
    apple: 'bg-forbidden-100 text-forbidden-700 border border-forbidden-200',
    snake: 'bg-gray-100 text-gray-700 border border-gray-200',
    leaf: 'bg-eden-100 text-eden-700 border border-eden-200',
    default: 'bg-paradise-100 text-paradise-700 border border-paradise-200'
  };

  const sizeClasses = {
    sm: 'px-2 py-1 text-xs',
    md: 'px-3 py-1.5 text-sm'
  };

  return (
    <span className={`${baseClasses} ${variantClasses[variant]} ${sizeClasses[size]} ${className}`}>
      {children}
    </span>
  );
};

export default Badge;
