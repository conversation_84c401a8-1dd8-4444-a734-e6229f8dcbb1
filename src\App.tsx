import React, { useState } from 'react';
import LoginScreen from './components/auth/LoginScreen';
import OnboardingFlow from './components/onboarding/OnboardingFlow';
import HomeScreen from './components/home/<USER>';
import MapScreen from './components/map/MapScreen';
import ChatScreen from './components/chat/ChatScreen';
import VideoCallScreen from './components/video/VideoCallScreen';
import ProfileScreen from './components/profile/ProfileScreen';
import { OnboardingData} from './types';

function App() {
  const [currentScreen, setCurrentScreen] = useState<string>('login');
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [hasCompletedOnboarding, setHasCompletedOnboarding] = useState(false);

  const handleLogin = () => {
    setIsAuthenticated(true);
    if (!hasCompletedOnboarding) {
      setCurrentScreen('onboarding');
    } else {
      setCurrentScreen('home');
    }
  };

  const handleSignup = () => {
    setIsAuthenticated(true);
    setCurrentScreen('onboarding');
  };

  const handleOnboardingComplete = (data: OnboardingData) => {
    setHasCompletedOnboarding(true);
    setCurrentScreen('home');
    // Here you would typically save the onboarding data
    console.log('Onboarding completed:', data);
  };

  const handleNavigate = (screen: string) => {
    setCurrentScreen(screen);
  };

  if (!isAuthenticated) {
    return <LoginScreen onLogin={handleLogin} onSignup={handleSignup} />;
  }

  if (!hasCompletedOnboarding) {
    return <OnboardingFlow onComplete={handleOnboardingComplete} />;
  }

  switch (currentScreen) {
    case 'home':
      return <HomeScreen onNavigate={handleNavigate} />;
    case 'map':
      return <MapScreen onNavigate={handleNavigate} />;
    case 'chat':
      return <ChatScreen onNavigate={handleNavigate} />;
    case 'video-call':
      return <VideoCallScreen onNavigate={handleNavigate} />;
    case 'profile':
      return <ProfileScreen onNavigate={handleNavigate} />;
    case 'confession':
      return (
        <div className="min-h-screen bg-gradient-to-br from-eden-50 via-paradise-50 to-forbidden-50 flex items-center justify-center p-4">
          <div className="text-center">
            <div className="text-6xl mb-4">🌳</div>
            <h2 className="text-2xl font-comic font-bold text-gray-800 mb-4">Confession Tree</h2>
            <p className="text-gray-600 font-inter mb-6">Coming soon... Share your forbidden thoughts anonymously</p>
            <button
              onClick={() => handleNavigate('home')}
              className="px-6 py-3 bg-eden-500 text-white rounded-full font-comic hover:bg-eden-600 transition-colors"
            >
              Back to Garden
            </button>
          </div>
        </div>
      );
    case 'snakebot':
      return (
        <div className="min-h-screen bg-gradient-to-br from-eden-50 via-paradise-50 to-forbidden-50 flex items-center justify-center p-4">
          <div className="text-center">
            <div className="text-6xl mb-4">🐍</div>
            <h2 className="text-2xl font-comic font-bold text-gray-800 mb-4">Snake Bot Wisdom</h2>
            <p className="text-gray-600 font-inter mb-6">Coming soon... Get tempting advice from our AI snake</p>
            <button
              onClick={() => handleNavigate('home')}
              className="px-6 py-3 bg-eden-500 text-white rounded-full font-comic hover:bg-eden-600 transition-colors"
            >
              Back to Garden
            </button>
          </div>
        </div>
      );
    default:
      return <HomeScreen onNavigate={handleNavigate} />;
  }
}

export default App;
