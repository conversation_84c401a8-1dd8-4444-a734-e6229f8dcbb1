# Change Log

All notable changes to this project will be documented in this file.
See [Conventional Commits](https://conventionalcommits.org) for commit guidelines.

## [2.3.3](https://github.com/ionic-team/ionic-cli/compare/@ionic/utils-terminal@2.3.2...@ionic/utils-terminal@2.3.3) (2022-06-16)

**Note:** Version bump only for package @ionic/utils-terminal





## [2.3.2](https://github.com/ionic-team/ionic-cli/compare/@ionic/utils-terminal@2.3.1...@ionic/utils-terminal@2.3.2) (2022-05-09)

**Note:** Version bump only for package @ionic/utils-terminal





## [2.3.1](https://github.com/ionic-team/ionic-cli/compare/@ionic/utils-terminal@2.3.0...@ionic/utils-terminal@2.3.1) (2020-09-29)


### Bug Fixes

* add signal-exit dependency in correct place ([178e5e5](https://github.com/ionic-team/ionic-cli/commit/178e5e51cdc3593e3d096a5197e1dc0e17292bbd))





# [2.3.0](https://github.com/ionic-team/ionic-cli/compare/@ionic/utils-terminal@2.2.1...@ionic/utils-terminal@2.3.0) (2020-09-24)


### Features

* **format:** add more formatting utilities ([d30f099](https://github.com/ionic-team/ionic-cli/commit/d30f099f50df18816fb1d3064c434f1b318518a2))
* **format:** terminal prose formatting utils ([8912348](https://github.com/ionic-team/ionic-cli/commit/8912348ca348ae6192ddfff1af88f9c9443d205d))





## [2.2.1](https://github.com/ionic-team/ionic-cli/compare/@ionic/utils-terminal@2.2.0...@ionic/utils-terminal@2.2.1) (2020-08-28)

**Note:** Version bump only for package @ionic/utils-terminal





# [2.2.0](https://github.com/ionic-team/ionic-cli/compare/@ionic/utils-terminal@2.1.4...@ionic/utils-terminal@2.2.0) (2020-08-27)


### Features

* add some ansi escape codes + cursor wrapper ([edbdb57](https://github.com/ionic-team/ionic-cli/commit/edbdb572bfe2fb5710eff7e49a483c86601ba425))





## [2.1.4](https://github.com/ionic-team/ionic-cli/compare/@ionic/utils-terminal@2.1.3...@ionic/utils-terminal@2.1.4) (2020-08-25)

**Note:** Version bump only for package @ionic/utils-terminal





## [2.1.3](https://github.com/ionic-team/ionic-cli/compare/@ionic/utils-terminal@2.1.2...@ionic/utils-terminal@2.1.3) (2020-05-12)


### Bug Fixes

* pin tslib to avoid "Cannot set property pathExists" error ([689e1f0](https://github.com/ionic-team/ionic-cli/commit/689e1f038b907356ef855a067a76d4822e7072a8))





## [2.1.2](https://github.com/ionic-team/ionic-cli/compare/@ionic/utils-terminal@2.1.1...@ionic/utils-terminal@2.1.2) (2020-05-06)

**Note:** Version bump only for package @ionic/utils-terminal





## [2.1.1](https://github.com/ionic-team/ionic-cli/compare/@ionic/utils-terminal@2.1.0...@ionic/utils-terminal@2.1.1) (2020-03-03)

**Note:** Version bump only for package @ionic/utils-terminal





# 2.1.0 (2020-02-11)


### Features

* **start:** add new list starter option ([#4315](https://github.com/ionic-team/ionic-cli/issues/4315)) ([1df44c1](https://github.com/ionic-team/ionic-cli/commit/1df44c1591f37b89f2b672857740edd6cb2aea67))





## [2.0.2](https://github.com/ionic-team/ionic-cli/compare/@ionic/utils-terminal@2.0.1...@ionic/utils-terminal@2.0.2) (2020-02-10)

**Note:** Version bump only for package @ionic/utils-terminal





## [2.0.1](https://github.com/ionic-team/ionic-cli/compare/@ionic/utils-terminal@2.0.0...@ionic/utils-terminal@2.0.1) (2020-02-03)

**Note:** Version bump only for package @ionic/utils-terminal





# [2.0.0](https://github.com/ionic-team/ionic-cli/compare/@ionic/utils-terminal@1.1.2...@ionic/utils-terminal@2.0.0) (2020-01-25)


### chore

* require Node 10 ([5a47874](https://github.com/ionic-team/ionic-cli/commit/5a478746c074207b6dc96aa8771f04a606deb1ef))


### BREAKING CHANGES

* A minimum of Node.js 10.3.0 is required.





## [1.1.2](https://github.com/ionic-team/ionic-cli/compare/@ionic/utils-terminal@1.1.1...@ionic/utils-terminal@1.1.2) (2019-12-10)


### Bug Fixes

* **windows:** do not check TERM for windows detection ([ac4b417](https://github.com/ionic-team/ionic-cli/commit/ac4b417385c0c7859674e2ba59e495e9abc5bce4))





## [1.1.1](https://github.com/ionic-team/ionic-cli/compare/@ionic/utils-terminal@1.1.0...@ionic/utils-terminal@1.1.1) (2019-12-05)

**Note:** Version bump only for package @ionic/utils-terminal





# [1.1.0](https://github.com/ionic-team/ionic-cli/compare/@ionic/utils-terminal@1.0.5...@ionic/utils-terminal@1.1.0) (2019-11-21)


### Bug Fixes

* **shell:** use shell from NodeJS if available ([53d0afa](https://github.com/ionic-team/ionic-cli/commit/53d0afaea8966f7742220896a98da570c706fb63))


### Features

* **ci:** support GitHub Actions detection ([5646c8e](https://github.com/ionic-team/ionic-cli/commit/5646c8e083862dbf976cd6cdecabe209c0ad8cfd))





## [1.0.5](https://github.com/ionic-team/ionic-cli/compare/@ionic/utils-terminal@1.0.4...@ionic/utils-terminal@1.0.5) (2019-09-18)

**Note:** Version bump only for package @ionic/utils-terminal





## [1.0.4](https://github.com/ionic-team/ionic-cli/compare/@ionic/utils-terminal@1.0.3...@ionic/utils-terminal@1.0.4) (2019-08-23)

**Note:** Version bump only for package @ionic/utils-terminal





## [1.0.3](https://github.com/ionic-team/ionic-cli/compare/@ionic/utils-terminal@1.0.2...@ionic/utils-terminal@1.0.3) (2019-08-14)

**Note:** Version bump only for package @ionic/utils-terminal





## [1.0.2](https://github.com/ionic-team/ionic-cli/compare/@ionic/utils-terminal@1.0.1...@ionic/utils-terminal@1.0.2) (2019-08-07)

**Note:** Version bump only for package @ionic/utils-terminal





## [1.0.1](https://github.com/ionic-team/ionic-cli/compare/@ionic/utils-terminal@1.0.0...@ionic/utils-terminal@1.0.1) (2019-06-05)

**Note:** Version bump only for package @ionic/utils-terminal





# [1.0.0](https://github.com/ionic-team/ionic-cli/compare/@ionic/utils-terminal@0.0.1...@ionic/utils-terminal@1.0.0) (2019-05-29)


### chore

* require Node 8 ([5670e68](https://github.com/ionic-team/ionic-cli/commit/5670e68))


### Features

* include path to shell in terminal info ([79480b9](https://github.com/ionic-team/ionic-cli/commit/79480b9))


### BREAKING CHANGES

* A minimum of Node.js 8.9.4 is required.





<a name="0.0.1"></a>
## 0.0.1 (2019-02-27)




**Note:** Version bump only for package @ionic/utils-terminal
