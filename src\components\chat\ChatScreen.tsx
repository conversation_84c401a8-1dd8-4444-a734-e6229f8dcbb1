import React, { useState } from 'react';
import Button from '../ui/Button';
import Card from '../ui/Card';
import { Message, Match } from '../../types';

interface ChatScreenProps {
  onNavigate: (screen: string) => void;
}

const ChatScreen: React.FC<ChatScreenProps> = ({ onNavigate }) => {
  const [selectedMatch, setSelectedMatch] = useState<Match | null>(null);
  const [newMessage, setNewMessage] = useState('');

  const matches: Match[] = [
    {
      id: '1',
      user: {
        id: '1',
        name: 'Eve',
        age: 25,
        bio: '',
        photos: ['https://images.unsplash.com/photo-1494790108755-2616b612b786?w=400'],
        location: { lat: 0, lng: 0 },
        temptationScore: 95,
        badges: ['Apple Lover'],
        isOnline: true
      },
      timestamp: new Date(),
      isRead: false
    },
    {
      id: '2',
      user: {
        id: '2',
        name: '<PERSON><PERSON>',
        age: 28,
        bio: '',
        photos: ['https://images.unsplash.com/photo-1534528741775-53994a69daeb?w=400'],
        location: { lat: 0, lng: 0 },
        temptationScore: 88,
        badges: ['Rebel'],
        isOnline: false
      },
      timestamp: new Date(Date.now() - 3600000),
      isRead: true
    }
  ];

  const sampleMessages: Message[] = [
    {
      id: '1',
      senderId: '1',
      content: 'Hey there! I see you like forbidden fruits too 🍎',
      timestamp: new Date(Date.now() - 1800000),
      type: 'text'
    },
    {
      id: '2',
      senderId: 'me',
      content: 'Absolutely! Want to share an apple sometime? 😏',
      timestamp: new Date(Date.now() - 1200000),
      type: 'text'
    },
    {
      id: '3',
      senderId: '1',
      content: '',
      timestamp: new Date(Date.now() - 600000),
      type: 'apple'
    }
  ];

  const sendMessage = () => {
    if (newMessage.trim()) {
      // Add message logic here
      setNewMessage('');
    }
  };

  const sendApple = () => {
    // Apple toss animation logic here
  };

  if (selectedMatch) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-eden-50 via-paradise-50 to-forbidden-50 flex flex-col">
        {/* Chat Header */}
        <div className="bg-white shadow-sm border-b border-eden-100 p-4">
          <div className="flex items-center gap-3 max-w-md mx-auto">
            <button
              onClick={() => setSelectedMatch(null)}
              className="p-2 rounded-full hover:bg-gray-100 transition-colors"
            >
              <i className="bi bi-arrow-left text-xl text-gray-600"></i>
            </button>
            
            <img
              src={selectedMatch.user.photos[0] || 'https://placehold.co/40x40/e5e7eb/6b7280?text=?'}
              alt={selectedMatch.user.name}
              className="w-10 h-10 rounded-full object-cover"
              crossOrigin="anonymous"
            />
            
            <div className="flex-1">
              <h2 className="font-comic font-semibold text-gray-800">
                {selectedMatch.user.name}
              </h2>
              <p className="text-xs text-gray-500">
                {selectedMatch.user.isOnline ? 'Online now' : 'Last seen recently'}
              </p>
            </div>
            
            <button className="p-2 rounded-full hover:bg-gray-100 transition-colors">
              <i className="bi bi-telephone text-xl text-gray-600"></i>
            </button>
            
            <button
              onClick={() => onNavigate('video-call')}
              className="p-2 rounded-full hover:bg-gray-100 transition-colors"
            >
              <i className="bi bi-camera-video text-xl text-gray-600"></i>
            </button>
          </div>
        </div>

        {/* Messages */}
        <div className="flex-1 p-4 max-w-md mx-auto w-full overflow-y-auto">
          <div className="space-y-4">
            {sampleMessages.map((message) => (
              <div
                key={message.id}
                className={`flex ${message.senderId === 'me' ? 'justify-end' : 'justify-start'}`}
              >
                <div
                  className={`max-w-xs px-4 py-2 rounded-2xl ${
                    message.senderId === 'me'
                      ? 'bg-eden-500 text-white'
                      : 'bg-white text-gray-800 border border-gray-200'
                  }`}
                >
                  {message.type === 'apple' ? (
                    <div className="text-center">
                      <div className="text-3xl animate-bounce">🍎</div>
                      <p className="text-xs mt-1 opacity-75">Sent a forbidden apple</p>
                    </div>
                  ) : (
                    <p className="font-inter text-sm">{message.content}</p>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Message Input */}
        <div className="bg-white border-t border-gray-200 p-4">
          <div className="flex items-center gap-3 max-w-md mx-auto">
            <button
              onClick={sendApple}
              className="p-3 rounded-full bg-forbidden-500 text-white hover:bg-forbidden-600 transition-colors"
            >
              <i className="bi bi-apple text-lg"></i>
            </button>
            
            <div className="flex-1 flex items-center bg-gray-100 rounded-full px-4 py-2">
              <input
                type="text"
                value={newMessage}
                onChange={(e) => setNewMessage(e.target.value)}
                placeholder="Type a tempting message..."
                className="flex-1 bg-transparent outline-none font-inter text-sm"
                onKeyPress={(e) => e.key === 'Enter' && sendMessage()}
              />
              <button className="p-1 text-gray-500 hover:text-gray-700">
                <i className="bi bi-emoji-smile"></i>
              </button>
            </div>
            
            <button
              onClick={sendMessage}
              disabled={!newMessage.trim()}
              className="p-3 rounded-full bg-eden-500 text-white hover:bg-eden-600 transition-colors disabled:opacity-50"
            >
              <i className="bi bi-send text-lg"></i>
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-eden-50 via-paradise-50 to-forbidden-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b border-eden-100 p-4">
        <div className="flex items-center justify-between max-w-md mx-auto">
          <button
            onClick={() => onNavigate('home')}
            className="p-2 rounded-full hover:bg-gray-100 transition-colors"
          >
            <i className="bi bi-arrow-left text-xl text-gray-600"></i>
          </button>
          
          <div className="text-center">
            <h1 className="text-xl font-comic font-bold text-gray-800">
              💬 Forbidden Chats
            </h1>
            <p className="text-xs text-gray-500">Your tempting conversations</p>
          </div>
          
          <button className="p-2 rounded-full hover:bg-gray-100 transition-colors">
            <i className="bi bi-search text-xl text-gray-600"></i>
          </button>
        </div>
      </div>

      {/* Matches List */}
      <div className="p-4 max-w-md mx-auto">
        <div className="space-y-3">
          {matches.map((match) => (
            <Card
              key={match.id}
              hover
              onClick={() => setSelectedMatch(match)}
              className="p-4"
            >
              <div className="flex items-center gap-4">
                <div className="relative">
                  <img
                    src={match.user.photos[0] || 'https://placehold.co/60x60/e5e7eb/6b7280?text=?'}
                    alt={match.user.name}
                    className="w-15 h-15 rounded-full object-cover"
                    crossOrigin="anonymous"
                  />
                  {match.user.isOnline && (
                    <div className="absolute -bottom-1 -right-1 w-4 h-4 bg-eden-500 rounded-full border-2 border-white"></div>
                  )}
                  {!match.isRead && (
                    <div className="absolute -top-1 -right-1 w-4 h-4 bg-forbidden-500 rounded-full"></div>
                  )}
                </div>
                
                <div className="flex-1">
                  <div className="flex items-center justify-between mb-1">
                    <h3 className="font-comic font-semibold text-gray-800">
                      {match.user.name}
                    </h3>
                    <span className="text-xs text-gray-400">
                      {match.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                    </span>
                  </div>
                  
                  <p className="text-sm text-gray-600 font-inter">
                    {match.user.name === 'Eve' 
                      ? '🍎 Sent a forbidden apple'
                      : 'Hey there! I see you like forbidden fruits too...'
                    }
                  </p>
                </div>
                
                <div className="text-right">
                  <i className="bi bi-chevron-right text-gray-400"></i>
                </div>
              </div>
            </Card>
          ))}
        </div>

        {matches.length === 0 && (
          <div className="text-center py-12">
            <div className="text-6xl mb-4">💔</div>
            <h3 className="text-xl font-comic font-semibold text-gray-600 mb-2">
              No matches yet
            </h3>
            <p className="text-gray-500 font-inter">
              Start swiping to find your forbidden match!
            </p>
            <Button
              onClick={() => onNavigate('home')}
              variant="forbidden"
              className="mt-4"
            >
              Start Matching
            </Button>
          </div>
        )}
      </div>
    </div>
  );
};

export default ChatScreen;
