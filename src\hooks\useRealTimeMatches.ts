import { useEffect, useState } from 'react';
import { supabase, Match, getUserMatches } from '../lib/supabase';
import { useAuth } from '../contexts/AuthContext';

export const useRealTimeMatches = () => {
  const [matches, setMatches] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const { user } = useAuth();

  // Load initial matches
  useEffect(() => {
    const loadMatches = async () => {
      if (!user) return;
      
      setLoading(true);
      const { data, error } = await getUserMatches(user.id);
      
      if (!error && data) {
        setMatches(data);
      }
      
      setLoading(false);
    };

    loadMatches();
  }, [user]);

  // Set up real-time subscription for matches
  useEffect(() => {
    if (!user) return;

    const channel = supabase
      .channel(`matches:${user.id}`)
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'matches',
          filter: `or(user1_id.eq.${user.id},user2_id.eq.${user.id})`
        },
        async (payload) => {
          // Reload matches when new match is created
          const { data } = await getUserMatches(user.id);
          if (data) {
            setMatches(data);
          }
        }
      )
      .on(
        'postgres_changes',
        {
          event: 'UPDATE',
          schema: 'public',
          table: 'matches',
          filter: `or(user1_id.eq.${user.id},user2_id.eq.${user.id})`
        },
        async (payload) => {
          // Reload matches when match status changes
          const { data } = await getUserMatches(user.id);
          if (data) {
            setMatches(data);
          }
        }
      )
      .subscribe();

    return () => {
      supabase.removeChannel(channel);
    };
  }, [user]);

  return {
    matches,
    loading
  };
};
