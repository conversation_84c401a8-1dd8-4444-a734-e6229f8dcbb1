# Change Log

All notable changes to this project will be documented in this file.
See [Conventional Commits](https://conventionalcommits.org) for commit guidelines.

## [2.1.11](https://github.com/ionic-team/ionic-cli/compare/@ionic/utils-subprocess@2.1.10...@ionic/utils-subprocess@2.1.11) (2022-06-16)

**Note:** Version bump only for package @ionic/utils-subprocess





## [2.1.10](https://github.com/ionic-team/ionic-cli/compare/@ionic/utils-subprocess@2.1.9...@ionic/utils-subprocess@2.1.10) (2022-05-09)

**Note:** Version bump only for package @ionic/utils-subprocess





## [2.1.9](https://github.com/ionic-team/ionic-cli/compare/@ionic/utils-subprocess@2.1.8...@ionic/utils-subprocess@2.1.9) (2022-03-04)


### Bug Fixes

* bump cross-spawn version to avoid windows problem ([#4784](https://github.com/ionic-team/ionic-cli/issues/4784)) ([2c88a71](https://github.com/ionic-team/ionic-cli/commit/2c88a7180e87497a27a97ca979c612d6663cbb2a))





## [2.1.8](https://github.com/ionic-team/ionic-cli/compare/@ionic/utils-subprocess@2.1.7...@ionic/utils-subprocess@2.1.8) (2020-09-29)

**Note:** Version bump only for package @ionic/utils-subprocess





## [2.1.7](https://github.com/ionic-team/ionic-cli/compare/@ionic/utils-subprocess@2.1.6...@ionic/utils-subprocess@2.1.7) (2020-09-24)

**Note:** Version bump only for package @ionic/utils-subprocess





## [2.1.6](https://github.com/ionic-team/ionic-cli/compare/@ionic/utils-subprocess@2.1.5...@ionic/utils-subprocess@2.1.6) (2020-08-28)

**Note:** Version bump only for package @ionic/utils-subprocess





## [2.1.5](https://github.com/ionic-team/ionic-cli/compare/@ionic/utils-subprocess@2.1.4...@ionic/utils-subprocess@2.1.5) (2020-08-27)

**Note:** Version bump only for package @ionic/utils-subprocess





## [2.1.4](https://github.com/ionic-team/ionic-cli/compare/@ionic/utils-subprocess@2.1.3...@ionic/utils-subprocess@2.1.4) (2020-08-25)

**Note:** Version bump only for package @ionic/utils-subprocess





## [2.1.3](https://github.com/ionic-team/ionic-cli/compare/@ionic/utils-subprocess@2.1.2...@ionic/utils-subprocess@2.1.3) (2020-05-12)


### Bug Fixes

* pin tslib to avoid "Cannot set property pathExists" error ([689e1f0](https://github.com/ionic-team/ionic-cli/commit/689e1f038b907356ef855a067a76d4822e7072a8))





## [2.1.2](https://github.com/ionic-team/ionic-cli/compare/@ionic/utils-subprocess@2.1.1...@ionic/utils-subprocess@2.1.2) (2020-05-06)

**Note:** Version bump only for package @ionic/utils-subprocess





## [2.1.1](https://github.com/ionic-team/ionic-cli/compare/@ionic/utils-subprocess@2.1.0...@ionic/utils-subprocess@2.1.1) (2020-03-03)

**Note:** Version bump only for package @ionic/utils-subprocess





# 2.1.0 (2020-02-11)


### Features

* **start:** add new list starter option ([#4315](https://github.com/ionic-team/ionic-cli/issues/4315)) ([1df44c1](https://github.com/ionic-team/ionic-cli/commit/1df44c1591f37b89f2b672857740edd6cb2aea67))





## [2.0.2](https://github.com/ionic-team/ionic-cli/compare/@ionic/utils-subprocess@2.0.1...@ionic/utils-subprocess@2.0.2) (2020-02-10)

**Note:** Version bump only for package @ionic/utils-subprocess





## [2.0.1](https://github.com/ionic-team/ionic-cli/compare/@ionic/utils-subprocess@2.0.0...@ionic/utils-subprocess@2.0.1) (2020-02-03)

**Note:** Version bump only for package @ionic/utils-subprocess





# [2.0.0](https://github.com/ionic-team/ionic-cli/compare/@ionic/utils-subprocess@1.0.13...@ionic/utils-subprocess@2.0.0) (2020-01-25)


### chore

* require Node 10 ([5a47874](https://github.com/ionic-team/ionic-cli/commit/5a478746c074207b6dc96aa8771f04a606deb1ef))


### BREAKING CHANGES

* A minimum of Node.js 10.3.0 is required.





## [1.0.13](https://github.com/ionic-team/ionic-cli/compare/@ionic/utils-subprocess@1.0.12...@ionic/utils-subprocess@1.0.13) (2020-01-13)

**Note:** Version bump only for package @ionic/utils-subprocess





## [1.0.12](https://github.com/ionic-team/ionic-cli/compare/@ionic/utils-subprocess@1.0.11...@ionic/utils-subprocess@1.0.12) (2019-12-10)

**Note:** Version bump only for package @ionic/utils-subprocess





## [1.0.11](https://github.com/ionic-team/ionic-cli/compare/@ionic/utils-subprocess@1.0.10...@ionic/utils-subprocess@1.0.11) (2019-12-05)

**Note:** Version bump only for package @ionic/utils-subprocess





## [1.0.10](https://github.com/ionic-team/ionic-cli/compare/@ionic/utils-subprocess@1.0.9...@ionic/utils-subprocess@1.0.10) (2019-11-21)

**Note:** Version bump only for package @ionic/utils-subprocess





## [1.0.9](https://github.com/ionic-team/ionic-cli/compare/@ionic/utils-subprocess@1.0.8...@ionic/utils-subprocess@1.0.9) (2019-10-14)

**Note:** Version bump only for package @ionic/utils-subprocess





## [1.0.8](https://github.com/ionic-team/ionic-cli/compare/@ionic/utils-subprocess@1.0.7...@ionic/utils-subprocess@1.0.8) (2019-09-18)

**Note:** Version bump only for package @ionic/utils-subprocess





## [1.0.7](https://github.com/ionic-team/ionic-cli/compare/@ionic/utils-subprocess@1.0.6...@ionic/utils-subprocess@1.0.7) (2019-08-28)

**Note:** Version bump only for package @ionic/utils-subprocess





## [1.0.6](https://github.com/ionic-team/ionic-cli/compare/@ionic/utils-subprocess@1.0.5...@ionic/utils-subprocess@1.0.6) (2019-08-23)

**Note:** Version bump only for package @ionic/utils-subprocess





## [1.0.5](https://github.com/ionic-team/ionic-cli/compare/@ionic/utils-subprocess@1.0.4...@ionic/utils-subprocess@1.0.5) (2019-08-14)

**Note:** Version bump only for package @ionic/utils-subprocess





## [1.0.4](https://github.com/ionic-team/ionic-cli/compare/@ionic/utils-subprocess@1.0.3...@ionic/utils-subprocess@1.0.4) (2019-08-07)

**Note:** Version bump only for package @ionic/utils-subprocess





## [1.0.3](https://github.com/ionic-team/ionic-cli/compare/@ionic/utils-subprocess@1.0.2...@ionic/utils-subprocess@1.0.3) (2019-06-28)

**Note:** Version bump only for package @ionic/utils-subprocess





## [1.0.2](https://github.com/ionic-team/ionic-cli/compare/@ionic/utils-subprocess@1.0.1...@ionic/utils-subprocess@1.0.2) (2019-06-10)

**Note:** Version bump only for package @ionic/utils-subprocess





## [1.0.1](https://github.com/ionic-team/ionic-cli/compare/@ionic/utils-subprocess@1.0.0...@ionic/utils-subprocess@1.0.1) (2019-06-05)

**Note:** Version bump only for package @ionic/utils-subprocess





# [1.0.0](https://github.com/ionic-team/ionic-cli/compare/@ionic/utils-subprocess@0.1.0...@ionic/utils-subprocess@1.0.0) (2019-05-29)


### Bug Fixes

* **subprocess:** check all executable extensions on windows ([#3949](https://github.com/ionic-team/ionic-cli/issues/3949)) ([e1cf74e](https://github.com/ionic-team/ionic-cli/commit/e1cf74e))


### chore

* require Node 8 ([5670e68](https://github.com/ionic-team/ionic-cli/commit/5670e68))


### BREAKING CHANGES

* A minimum of Node.js 8.9.4 is required.





<a name="0.1.0"></a>
# [0.1.0](https://github.com/ionic-team/ionic-cli/compare/@ionic/utils-subprocess@0.0.1...@ionic/utils-subprocess@0.1.0) (2019-03-06)


### Bug Fixes

* **subprocess:** allow symlinks for commands found by `which` ([ce5bf80](https://github.com/ionic-team/ionic-cli/commit/ce5bf80))
* **subprocess:** check if command found by `which` is executable ([638cf61](https://github.com/ionic-team/ionic-cli/commit/638cf61))


### Features

* **subprocess:** add `convertPATH` utility ([eb14e8d](https://github.com/ionic-team/ionic-cli/commit/eb14e8d))
* **subprocess:** add `findExecutables` (like `which`, but for all) ([95acb53](https://github.com/ionic-team/ionic-cli/commit/95acb53))
* **subprocess:** options for `bashify()` ([465fafc](https://github.com/ionic-team/ionic-cli/commit/465fafc))




<a name="0.0.1"></a>
## 0.0.1 (2019-02-27)




**Note:** Version bump only for package @ionic/utils-subprocess
