import React, { useState } from 'react';
import Button from '../ui/Button';
import Card from '../ui/Card';

interface LoginScreenProps {
  onLogin: () => void;
  onSignup: () => void;
}

const LoginScreen: React.FC<LoginScreenProps> = ({ onLogin, onSignup }) => {
  const [isLogin, setIsLogin] = useState(true);

  return (
    <div className="min-h-screen bg-gradient-to-br from-eden-50 via-paradise-50 to-forbidden-50 flex items-center justify-center p-4">
      {/* Floating decorations */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute top-20 left-10 text-4xl animate-float">🍎</div>
        <div className="absolute top-40 right-20 text-3xl animate-float" style={{ animationDelay: '1s' }}>🐍</div>
        <div className="absolute bottom-32 left-20 text-3xl animate-float" style={{ animationDelay: '2s' }}>🌿</div>
        <div className="absolute top-60 left-1/2 text-2xl animate-float" style={{ animationDelay: '0.5s' }}>🌳</div>
      </div>

      <Card className="w-full max-w-md p-8 relative z-10">
        <div className="text-center mb-8">
          <div className="text-6xl mb-4">🍎</div>
          <h1 className="text-3xl font-comic font-bold text-gray-800 mb-2">
            Adem & Eve
          </h1>
          <p className="text-lg font-comic text-gray-600">
            Forbidden Match
          </p>
          <p className="text-sm text-gray-500 mt-2">
            Ready to sin a little? 😈
          </p>
        </div>

        <div className="space-y-4">
          {isLogin ? (
            <>
              <div>
                <input
                  type="email"
                  placeholder="Your forbidden email..."
                  className="w-full px-4 py-3 rounded-xl border border-gray-300 focus:border-eden-500 focus:outline-none font-inter"
                />
              </div>
              <div>
                <input
                  type="password"
                  placeholder="Secret garden password..."
                  className="w-full px-4 py-3 rounded-xl border border-gray-300 focus:border-eden-500 focus:outline-none font-inter"
                />
              </div>
              <Button
                onClick={onLogin}
                variant="forbidden"
                size="lg"
                className="w-full"
                icon="bi bi-apple"
              >
                Bite the Apple
              </Button>
            </>
          ) : (
            <>
              <div>
                <input
                  type="text"
                  placeholder="Your tempting name..."
                  className="w-full px-4 py-3 rounded-xl border border-gray-300 focus:border-eden-500 focus:outline-none font-inter"
                />
              </div>
              <div>
                <input
                  type="email"
                  placeholder="Your forbidden email..."
                  className="w-full px-4 py-3 rounded-xl border border-gray-300 focus:border-eden-500 focus:outline-none font-inter"
                />
              </div>
              <div>
                <input
                  type="password"
                  placeholder="Create secret password..."
                  className="w-full px-4 py-3 rounded-xl border border-gray-300 focus:border-eden-500 focus:outline-none font-inter"
                />
              </div>
              <Button
                onClick={onSignup}
                variant="paradise"
                size="lg"
                className="w-full"
                icon="bi bi-plus-circle"
              >
                Join the Temptation
              </Button>
            </>
          )}

          <div className="text-center">
            <button
              onClick={() => setIsLogin(!isLogin)}
              className="text-eden-600 hover:text-eden-700 font-comic text-sm underline"
            >
              {isLogin ? "New to paradise? Create account" : "Already tempted? Sign in"}
            </button>
          </div>
        </div>

        <div className="mt-8 text-center">
          <p className="text-xs text-gray-500 font-inter">
            "And they were both naked, the man and his wife, and were not ashamed." 
            <br />
            - Genesis 2:25 (But now with WiFi) 📱
          </p>
        </div>
      </Card>
    </div>
  );
};

export default LoginScreen;
