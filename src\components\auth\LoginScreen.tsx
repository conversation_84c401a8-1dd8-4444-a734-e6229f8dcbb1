import React, { useState } from 'react';
import Button from '../ui/Button';
import Card from '../ui/Card';
import { useAuth } from '../../contexts/AuthContext';

interface LoginScreenProps {
  onLogin: () => void;
  onSignup: () => void;
}

const LoginScreen: React.FC<LoginScreenProps> = ({ onLogin, onSignup }) => {
  const [isLogin, setIsLogin] = useState(true);
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [name, setName] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  const { signIn, signUp } = useAuth();

  const handleLogin = async () => {
    if (!email || !password) {
      setError('Please fill in all fields');
      return;
    }

    setLoading(true);
    setError('');

    const { error } = await signIn(email, password);

    if (error) {
      setError(error.message);
    } else {
      onLogin();
    }

    setLoading(false);
  };

  const handleSignup = async () => {
    if (!name || !email || !password) {
      setError('Please fill in all fields');
      return;
    }

    if (password.length < 6) {
      setError('Password must be at least 6 characters');
      return;
    }

    setLoading(true);
    setError('');

    const { error } = await signUp(email, password, {
      name,
      age: 25, // Default age, will be updated in onboarding
      bio: '',
      photos: [],
      preferences: {
        ageRange: [18, 50],
        maxDistance: 50,
        interests: []
      },
      isOnline: true
    });

    if (error) {
      setError(error.message);
    } else {
      onSignup();
    }

    setLoading(false);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-eden-50 via-paradise-50 to-forbidden-50 flex items-center justify-center p-4">
      {/* Floating decorations */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute top-20 left-10 text-4xl animate-float">🍎</div>
        <div className="absolute top-40 right-20 text-3xl animate-float" style={{ animationDelay: '1s' }}>🐍</div>
        <div className="absolute bottom-32 left-20 text-3xl animate-float" style={{ animationDelay: '2s' }}>🌿</div>
        <div className="absolute top-60 left-1/2 text-2xl animate-float" style={{ animationDelay: '0.5s' }}>🌳</div>
      </div>

      <Card className="w-full max-w-md p-8 relative z-10">
        <div className="text-center mb-8">
          <div className="text-6xl mb-4">🍎</div>
          <h1 className="text-3xl font-comic font-bold text-gray-800 mb-2">
            Adem & Eve
          </h1>
          <p className="text-lg font-comic text-gray-600">
            Forbidden Match
          </p>
          <p className="text-sm text-gray-500 mt-2">
            Ready to sin a little? 😈
          </p>
        </div>

        <div className="space-y-4">
          {error && (
            <div className="bg-forbidden-100 border border-forbidden-300 text-forbidden-700 px-4 py-3 rounded-xl text-sm">
              {error}
            </div>
          )}

          {isLogin ? (
            <>
              <div>
                <input
                  type="email"
                  placeholder="Your forbidden email..."
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  className="w-full px-4 py-3 rounded-xl border border-gray-300 focus:border-eden-500 focus:outline-none font-inter"
                  disabled={loading}
                />
              </div>
              <div>
                <input
                  type="password"
                  placeholder="Secret garden password..."
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  className="w-full px-4 py-3 rounded-xl border border-gray-300 focus:border-eden-500 focus:outline-none font-inter"
                  disabled={loading}
                />
              </div>
              <Button
                onClick={handleLogin}
                variant="forbidden"
                size="lg"
                className="w-full"
                icon="bi bi-apple"
                disabled={loading}
              >
                {loading ? 'Biting...' : 'Bite the Apple'}
              </Button>
            </>
          ) : (
            <>
              <div>
                <input
                  type="text"
                  placeholder="Your tempting name..."
                  value={name}
                  onChange={(e) => setName(e.target.value)}
                  className="w-full px-4 py-3 rounded-xl border border-gray-300 focus:border-eden-500 focus:outline-none font-inter"
                  disabled={loading}
                />
              </div>
              <div>
                <input
                  type="email"
                  placeholder="Your forbidden email..."
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  className="w-full px-4 py-3 rounded-xl border border-gray-300 focus:border-eden-500 focus:outline-none font-inter"
                  disabled={loading}
                />
              </div>
              <div>
                <input
                  type="password"
                  placeholder="Create secret password..."
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  className="w-full px-4 py-3 rounded-xl border border-gray-300 focus:border-eden-500 focus:outline-none font-inter"
                  disabled={loading}
                />
              </div>
              <Button
                onClick={handleSignup}
                variant="paradise"
                size="lg"
                className="w-full"
                icon="bi bi-plus-circle"
                disabled={loading}
              >
                {loading ? 'Joining...' : 'Join the Temptation'}
              </Button>
            </>
          )}

          <div className="text-center">
            <button
              onClick={() => setIsLogin(!isLogin)}
              className="text-eden-600 hover:text-eden-700 font-comic text-sm underline"
            >
              {isLogin ? "New to paradise? Create account" : "Already tempted? Sign in"}
            </button>
          </div>
        </div>

        <div className="mt-8 text-center">
          <p className="text-xs text-gray-500 font-inter">
            "And they were both naked, the man and his wife, and were not ashamed." 
            <br />
            - Genesis 2:25 (But now with WiFi) 📱
          </p>
        </div>
      </Card>
    </div>
  );
};

export default LoginScreen;
