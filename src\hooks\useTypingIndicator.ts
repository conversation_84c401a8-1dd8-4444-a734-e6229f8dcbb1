import { useEffect, useState, useCallback } from 'react';
import { supabase } from '../lib/supabase';
import { useAuth } from '../contexts/AuthContext';

interface UseTypingIndicatorProps {
  matchId: string;
}

export const useTypingIndicator = ({ matchId }: UseTypingIndicatorProps) => {
  const [isTyping, setIsTyping] = useState(false);
  const [otherUserTyping, setOtherUserTyping] = useState(false);
  const { user } = useAuth();

  // Listen for typing events from other user
  useEffect(() => {
    if (!matchId || !user) return;

    const channel = supabase
      .channel(`typing:${matchId}`)
      .on('broadcast', { event: 'typing' }, (payload) => {
        if (payload.payload.userId !== user.id) {
          setOtherUserTyping(payload.payload.isTyping);
          
          // Auto-hide typing indicator after 3 seconds
          if (payload.payload.isTyping) {
            setTimeout(() => {
              setOtherUserTyping(false);
            }, 3000);
          }
        }
      })
      .subscribe();

    return () => {
      supabase.removeChannel(channel);
    };
  }, [matchId, user]);

  // Send typing indicator
  const sendTypingIndicator = useCallback((typing: boolean) => {
    if (!matchId || !user) return;

    setIsTyping(typing);
    
    supabase.channel(`typing:${matchId}`).send({
      type: 'broadcast',
      event: 'typing',
      payload: {
        userId: user.id,
        isTyping: typing
      }
    });
  }, [matchId, user]);

  // Auto-stop typing after delay
  useEffect(() => {
    if (isTyping) {
      const timer = setTimeout(() => {
        sendTypingIndicator(false);
      }, 2000);
      
      return () => clearTimeout(timer);
    }
  }, [isTyping, sendTypingIndicator]);

  return {
    isTyping,
    otherUserTyping,
    sendTypingIndicator
  };
};
