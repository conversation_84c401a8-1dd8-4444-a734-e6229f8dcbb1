# Android Release Signing Guide

## Generate Release Keystore

To create a signed APK for Google Play Store, you need to generate a release keystore:

```bash
# Navigate to android/app directory
cd android/app

# Generate keystore (replace with your details)
keytool -genkey -v -keystore my-release-key.keystore -alias my-key-alias -keyalg RSA -keysize 2048 -validity 10000

# You'll be prompted for:
# - Keystore password (remember this!)
# - Key password (remember this!)
# - Your name and organization details
```

## Update build.gradle

Add signing configuration to `android/app/build.gradle`:

```gradle
android {
    ...
    signingConfigs {
        release {
            if (project.hasProperty('MYAPP_RELEASE_STORE_FILE')) {
                storeFile file(MYAPP_RELEASE_STORE_FILE)
                storePassword MYAPP_RELEASE_STORE_PASSWORD
                keyAlias MYAPP_RELEASE_KEY_ALIAS
                keyPassword MYAPP_RELEASE_KEY_PASSWORD
            }
        }
    }
    buildTypes {
        release {
            ...
            signingConfig signingConfigs.release
        }
    }
}
```

## Create gradle.properties

Create `android/gradle.properties` with your keystore details:

```properties
MYAPP_RELEASE_STORE_FILE=my-release-key.keystore
MYAPP_RELEASE_KEY_ALIAS=my-key-alias
MYAPP_RELEASE_STORE_PASSWORD=your_keystore_password
MYAPP_RELEASE_KEY_PASSWORD=your_key_password
```

## Build Release APK

```bash
# Build release APK
cd android
./gradlew assembleRelease

# Or build AAB (recommended for Play Store)
./gradlew bundleRelease
```

## Important Notes

1. **Keep your keystore safe** - You cannot update your app without it
2. **Use strong passwords** - Store them securely
3. **Backup your keystore** - Store in multiple secure locations
4. **Never commit keystore to version control**
5. **Use AAB format** - Google Play prefers Android App Bundle

## File Locations

After successful build:
- APK: `android/app/build/outputs/apk/release/app-release.apk`
- AAB: `android/app/build/outputs/bundle/release/app-release.aab`

## Google Play Console Upload

1. Create Google Play Console account
2. Create new application
3. Upload AAB file (preferred) or APK
4. Fill in store listing details
5. Set up content rating
6. Configure pricing and distribution
7. Submit for review
